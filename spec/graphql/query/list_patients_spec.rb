require "rails_helper"

RSpec.describe "query patients", type: :graphql do
  it "retrieves patients" do
    org_user = create(:organisation_user)
    org_user_2 = create(:organisation_user)

    patient_1 = create(:patient, organisation: org_user.organisation)
    patient_2 = create(:patient, organisation: org_user.organisation)
    patient_3 = create(:patient, organisation: org_user_2.organisation)

    result = execute_graphql(query, variables: {
      organisationId: org_user.organisation.id
    }, user: org_user.user)

    expect(result.dig("data", "patients").size).to be(2)
  end

  it "return an error when request fails" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = execute_graphql(query, variables: {
      organisationId: "not_id"
    }, user: org_user.user)

    expect(result["data"]["patients"]).to be_empty
  end

  private

  def query
    <<~GQL
      query listPatients($organisationId: ID!){
          patients(organisationId: $organisationId){
          id
          identificationNumber
        }
      }
    GQL
  end
end
