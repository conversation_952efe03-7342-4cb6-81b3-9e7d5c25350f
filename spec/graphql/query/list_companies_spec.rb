require "rails_helper"

RSpec.describe "query companys", type: :graphql do
  it "retrieves companys" do
    org_user = create(:organisation_user)
    org_user_2 = create(:organisation_user)

    company_1 = create(:company, organisation: org_user.organisation)
    company_2 = create(:company, organisation: org_user.organisation)
    company_3 = create(:company, organisation: org_user_2.organisation)

    result = execute_graphql(query, variables: {
      organisationId: org_user.organisation.id
    }, user: org_user.user)

    expect(result.dig("data", "companies").size).to be(2)
  end

  it "return an error when request fails" do
    user = create(:user)
    result = execute_graphql(query, variables: {
      organisationId: "not_id"
    }, user: user)

    expect(result["data"]["companies"]).to be_empty
  end

  private

  def query
    <<~GQL
      query Companies($organisationId: ID!) {
        companies(organisationId: $organisationId) {
          name
        }
      }
    GQL
  end
end
