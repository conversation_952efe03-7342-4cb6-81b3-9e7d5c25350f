require "rails_helper"

RSpec.describe "query company", type: :graphql do
  it "successfully" do
    org_user = create(:organisation_user)
    company = create(:company, organisation: org_user.organisation)

    result = execute_graphql(query, variables: {
      id: company.id
    }, user: org_user.user)

    expect(result.dig("data", "company", "name")).not_to be_blank
  end

  it "return an error when request fails" do
    org_user = create(:organisation_user)
    company = create(:company, organisation: org_user.organisation)

    result = execute_graphql(query, variables: {
      id: 2
    }, user: org_user.user)

    expect(result["data"]["company"]).to be_nil
    expect(result["error"]).to be_nil
  end

  private

  def query
    <<~GQL
      query getCompany($id: ID!) {  
        company(id: $id ) {
         name 
        }
      }
    GQL
  end
end
