require "rails_helper"

RSpec.describe "mutation delete patient", type: :graphql do
  it "successfully" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = execute_graphql(query, variables: {
      patient_id: patient.id
    }, user: org_user.user)

    expect(result["data"]["deletePatient"]["patient"]["id"]).to eql(patient.id.to_s)
    expect(result["errors"]).to be_nil
    expect(result["data"]["deletePatient"]["success"]).to be_truthy
  end

  it "return an error when not found" do
    user = create(:user)
    result = execute_graphql(query, variables: {
      patient_id: "not_id"
    }, user: user)

    expect(result["data"]["deletePatient"]["success"]).to be_falsy
    expect(result["data"]["deletePatient"]["patient"]).to be_nil
    expect(result["data"]["deletePatient"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation deletePatient($patient_id: ID!){
        deletePatient(id: $patient_id){
          success
          patient{
           id 
          }
          errors{
            path
            message
          }
        }
      } 
    GQL
  end
end
