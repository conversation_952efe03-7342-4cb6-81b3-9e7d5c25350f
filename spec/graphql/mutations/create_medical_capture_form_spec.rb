require "rails_helper"

RSpec.describe "CreateMedicalCaptureForm", type: :graphql do
  it "successfully" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    clinic = create(:clinic, organisation: org_user.organisation)
    employment = create(:employment, patient: patient)

    result = execute_graphql(query, variables: {
      input: {
        audioPerformed: true,
        cannabisPerformed: true,
        ecgPerformed: true,
        heatPerformed: false,
        heightPerformed: false,
        physicalExamPerformed: true,
        visionScreeningPerformed: false,
        spiroPerformed: true,
        xrayPerformed: true,

        exclusionComment: "exclusion comment",
        exclusion: true,

        patientId: patient.id.to_s,
        employmentId: employment.id.to_s,
        clinicId: clinic.id.to_s,

        medicalType: "PRE_EMPLOYMENT",
        name: "123123",

        referral: true,
        referralComment: "referral comment",

        medicalExaminationDate: Date.yesterday.iso8601,
        medicalExpiryDate: Date.tomorrow.iso8601,

        outcome: "FIT",
        outcomeComment: true,
        outcomeCommentText: "Good result"
      }
    }, user: org_user.user)

    # expect(result["data"]["createMedicalCaptureForm"]["patient"]["lastName"]).to eql("xyz")
    expect(result["errors"]).to be_nil
    expect(result["data"]["createMedicalCaptureForm"]["success"]).to be_truthy
  end

  xit "return an error when not valid" do
    patient = create(:patient)
    clinic = create(:clinic)
    employment = create(:employment, patient: patient)

    result = OccumedSchema.execute(query, variables: {
      organisationId: org_user.organisation.id,
      input: {
        firstName: "abc",
        lastName: "xyz",
        identificationNumber: "qweqwe123",
        dob: Date.yesterday,
        gender: "",

        employmentDepartment: "general",
        employmentStartDate: Date.today,
        employmentPosition: "manager",

        companyCapture: "add_company",
        companyInput: "Tesla"
      }
    })

    expect(result["data"]["createPatientCaptureForm"]["success"]).to be_falsy
    expect(result["data"]["createPatientCaptureForm"]["patient"]).to be_nil
    expect(result["data"]["createPatientCaptureForm"]["errors"][0]["path"]).to eql("gender")
  end

  xit "return an error when not found" do
    result = OccumedSchema.execute(query, variables: {
      organisationId: "not-exist",
      input: {
        firstName: "abc",
        lastName: "xyz",
        identificationNumber: "qweqwe123",
        dob: Date.yesterday,
        gender: "male",

        employmentDepartment: "general",
        employmentStartDate: Date.today,
        employmentPosition: "manager",

        companyCapture: "add_company",
        companyInput: "Tesla"
      }
    })

    expect(result["data"]["createPatientCaptureForm"]["success"]).to be_falsy
    expect(result["data"]["createPatientCaptureForm"]["patient"]).to be_nil
    expect(result["data"]["createPatientCaptureForm"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation createMedical($input: MedicalCaptureFormInput!) {
        createMedicalCaptureForm(input: $input) {
          success
          errors {
            path
            message
          }
          medical {
            status
          }
        }
      } 
    GQL
  end
end
