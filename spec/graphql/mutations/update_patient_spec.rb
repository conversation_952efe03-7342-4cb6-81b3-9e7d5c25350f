require "rails_helper"

RSpec.describe "mutation update patient", type: :graphql do
  it "successfully" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = execute_graphql(query, variables: {
      id: patient.id,
      input: {
        lastName: "jacks"
      }
    }, user: org_user.user)

    expect(result["data"]["updatePatient"]["patient"]["lastName"]).to eql("jacks")
    expect(result["errors"]).to be_nil
    expect(result["data"]["updatePatient"]["success"]).to be_truthy
  end

  it "return an error when not valid" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = execute_graphql(query, variables: {
      id: patient.id,
      input: {
        lastName: ""
      }
    }, user: org_user.user)

    expect(result["data"]["updatePatient"]["success"]).to be_falsy
    expect(result["data"]["updatePatient"]["patient"]).to be_nil
    expect(result["data"]["updatePatient"]["errors"][0]["path"]).to eql("lastName")
  end

  it "return an error when not found" do
    user = create(:user)
    result = execute_graphql(query, variables: {
      id: 1,
      input: {
        lastName: "jacks"
      }
    }, user: user)

    expect(result["data"]["updatePatient"]["success"]).to be_falsy
    expect(result["data"]["updatePatient"]["patient"]).to be_nil
    expect(result["data"]["updatePatient"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation updatePatient($id: ID!, $input:PatientInput!){
        updatePatient(id: $id, input: $input){
          success
          patient{
            identificationNumber
            firstName
            lastName
          }
          errors{
            path
            message
          }
        }
      }
    GQL
  end
end
