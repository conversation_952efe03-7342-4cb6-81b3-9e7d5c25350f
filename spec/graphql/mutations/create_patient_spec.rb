require "rails_helper"

RSpec.describe "mutation update patient" do
  it "successfully" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      organisationId: org_user.organisation.id,
      input: {
        lastName: "jacks",
        firstName: "toes",
        dob: "2012-09-27",
        identificationNumber: "13245691",
        gender: "Male"
      }
    }, context: { requester: org_user.user })

    expect(result["data"]["createPatient"]["patient"]["lastName"]).to eql("jacks")
    expect(result["errors"]).to be_nil
    expect(result["data"]["createPatient"]["success"]).to be_truthy
  end

  it "return an error when not valid" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      organisationId: org_user.organisation.id,
      input: {
        lastName: "",
        firstName: "toes",
        dob: "2012-09-27",
        identificationNumber: "13245691",
        gender: "Male"
      }
    }, context: { requester: org_user.user })

    expect(result["data"]["createPatient"]["success"]).to be_falsy
    expect(result["data"]["createPatient"]["patient"]).to be_nil
    expect(result["data"]["createPatient"]["errors"][0]["path"]).to eql("lastName")
  end

  it "return an error when not found" do
    user = create(:user)
    result = OccumedSchema.execute(query, variables: {
      organisationId: "not-exist",
      input: {
        lastName: "",
        firstName: "toes",
        dob: "2012-09-27",
        identificationNumber: "13245691",
        gender: "Male"
      }
    }, context: { requester: user })

    expect(result["data"]["createPatient"]["success"]).to be_falsy
    expect(result["data"]["createPatient"]["patient"]).to be_nil
    expect(result["data"]["createPatient"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation createPatient($organisationId: ID!, $input:PatientInput!){
        createPatient(organisationId: $organisationId, input: $input){
          success
          patient{
            identificationNumber
            firstName
            lastName
          }
          errors{
            path
            message
          }
        }
      }
    GQL
  end
end
