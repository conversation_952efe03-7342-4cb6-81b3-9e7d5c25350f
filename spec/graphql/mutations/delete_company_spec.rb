require "rails_helper"

RSpec.describe "mutation delete company", type: :graphql do
  it "successfully" do
    org_user = create(:organisation_user)
    company = create(:company, organisation: org_user.organisation)

    result = execute_graphql(query, variables: {
      id: company.id
    }, user: org_user.user)

    expect(result["data"]["deleteCompany"]["company"]["id"]).to eql(company.id.to_s)
    expect(result["errors"]).to be_nil
    expect(result["data"]["deleteCompany"]["success"]).to be_truthy
  end

  it "return an error when not found" do
    user = create(:user)
    result = execute_graphql(query, variables: {
      id: "not_id"
    }, user: user)

    expect(result["data"]["deleteCompany"]["success"]).to be_falsy
    expect(result["data"]["deleteCompany"]["company"]).to be_nil
    expect(result["data"]["deleteCompany"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation deleteCompany($id: ID!) {
        deleteCompany(id: $id) {
          errors {
            path
            message
          }
          company {
            name
            id
          }
          success
        }
      }    
    GQL
  end
end
