require "rails_helper"

RSpec.describe "mutation create patient capture form" do
  it "successfully" do
    org_user = create(:organisation_user)

    result = OccumedSchema.execute(query, variables: {
      organisationId: org_user.organisation.id,
      input: {
        firstName: "abc",
        lastName: "xyz",
        identificationNumber: "qweqwe123",
        dob: Date.yesterday,
        gender: "male",

        employmentDepartment: "general",
        employmentStartDate: Date.today,
        employmentPosition: "manager",

        companyCapture: "add_company",
        companyInput: "Tesla"
      }
    }, context: { requester: org_user.user })

    expect(result["data"]["createPatientCaptureForm"]["patient"]["lastName"]).to eql("xyz")
    expect(result["errors"]).to be_nil
    expect(result["data"]["createPatientCaptureForm"]["success"]).to be_truthy
  end

  it "return an error when not valid" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      organisationId: org_user.organisation.id,
      input: {
        firstName: "abc",
        lastName: "xyz",
        identificationNumber: "qweqwe123",
        dob: Date.yesterday,
        gender: "",

        employmentDepartment: "general",
        employmentStartDate: Date.today,
        employmentPosition: "manager",

        companyCapture: "add_company",
        companyInput: "Tesla"
      }
    }, context: { requester: org_user.user })

    expect(result["data"]["createPatientCaptureForm"]["success"]).to be_falsy
    expect(result["data"]["createPatientCaptureForm"]["patient"]).to be_nil
    expect(result["data"]["createPatientCaptureForm"]["errors"][0]["path"]).to eql("gender")
  end

  it "return an error when not found" do
    user = create(:user)
    result = OccumedSchema.execute(query, variables: {
      organisationId: "not-exist",
      input: {
        firstName: "abc",
        lastName: "xyz",
        identificationNumber: "qweqwe123",
        dob: Date.yesterday,
        gender: "male",

        employmentDepartment: "general",
        employmentStartDate: Date.today,
        employmentPosition: "manager",

        companyCapture: "add_company",
        companyInput: "Tesla"
      }
    }, context: { requester: user })

    expect(result["data"]["createPatientCaptureForm"]["success"]).to be_falsy
    expect(result["data"]["createPatientCaptureForm"]["patient"]).to be_nil
    expect(result["data"]["createPatientCaptureForm"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation createPatientCaptureForm($organisationId: ID!,$input: PatientCaptureFormInput!){
        createPatientCaptureForm(organisationId: $organisationId, input: $input){
          success
          errors{
            path
            message
          }
          patient{
            lastName
            firstName
            
            employments{
              position
              department
            }
            
            employers{
              name
            }
          }
          
        }
      } 
    GQL
  end
end
