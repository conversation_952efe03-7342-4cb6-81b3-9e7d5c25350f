# frozen_string_literal: true

# This file is copied to spec/ when you run 'rails generate rspec:install'
ENV["RAILS_ENV"] ||= "test"
require "spec_helper"
require File.expand_path("../config/environment", __dir__)
# Prevent database truncation if the environment is production
abort("The Rails environment is running in production mode!") if Rails.env.production?

require "rspec/rails"
require "capybara/rspec"

# Add additional requires below this line. Rails is not loaded until this point!
require "sidekiq/testing"

require "capybara/cuprite"

Capybara.register_driver(:cuprite) do |app|
  Capybara::Cuprite::Driver.new(
    app,
    window_size: [1200, 800],
    browser_options: {},
    # Increase Chrome startup timeout for CI
    process_timeout: 10,
    inspector: true,
    # Allow running Chrome in a headful mode by setting HEADLESS env
    # var to a falsey value
    headless: !ENV["HEADLESS"].in?(%w[n 0 no false])
  )
end

Capybara.default_driver = Capybara.javascript_driver = :cuprite
Capybara.server = :webrick

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
#
Dir[Rails.root.join("spec", "support", "**", "*.rb")].sort.each { |f| require f }

# Checks for pending migrations and applies them before tests are run.
# If you are not using ActiveRecord, you can remove these lines.
begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  puts e.to_s.strip
  exit 1
end

Capybara.default_max_wait_time = 5

RSpec.configure do |config|
  config.use_transactional_fixtures = true

  # config.fixture_path = Rails.root.join("spec/fixtures")
  config.file_fixture_path = Rails.root.join("spec/fixtures/files").to_s

  config.include Warden::Test::Helpers
  config.include ApplicationHelper
  config.include ActionMailer::TestHelper
  config.include Devise::Test::IntegrationHelpers
  config.include FactoryBot::Syntax::Methods
  config.include CupriteHelpers
  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  # config.before(:suite) do
  #   if config.use_transactional_fixtures?
  #     raise(<<-MSG)
  #       Delete line `config.use_transactional_fixtures = true` from rails_helper.rb
  #       (or set it to false) to prevent uncommitted transactions being used in
  #       JavaScript-dependent specs.

  #       During testing, the app-under-test that the browser driver connects to
  #       uses a different database connection to the database connection used by
  #       the spec. The app's database connection would not be able to access
  #       uncommitted transaction data setup over the spec's database connection.
  #     MSG
  #   end
  #   DatabaseCleaner.clean_with(:truncation)
  # end

  config.prepend_before(:each, type: :system) do
    # Use JS driver always
    driven_by Capybara.javascript_driver
  end

  config.before(:suite) do
    # if Webpacker.dev_server.running?
    #   $stdout.puts "\n⚙️  Webpack dev server is running! Skip assets compilation.\n"
    #   next
    # else
    #   $stdout.puts "\n🐢  Precompiling assets.\n"
    #   original_stdout = $stdout.clone
    #   # Use test-prof now 'cause it couldn't be monkey-patched (e.g., by Timecop or similar)
    #   start = Time.current
    #   begin
    #     # Silence Webpacker output
    #     $stdout.reopen(File.new("/dev/null", "w"))
    #     # next 3 lines to compile webpacker before running our test suite
    # Rails.application.load_tasks
    # Rake::Task["assets:precompile"].invoke
    #   ensure
    #     $stdout.reopen(original_stdout)
    #     $stdout.puts "Finished in #{(Time.current - start).round(2)} seconds"
    #   end
    # end
  end

  config.before do
    Warden.test_reset!
    Sidekiq::Worker.clear_all
  end

  # You can uncomment this line to turn off ActiveRecord support entirely.
  # config.use_active_record = false

  # RSpec Rails can automatically mix in different behaviours to your tests
  # based on their file location, for example enabling you to call `get` and
  # `post` in specs under `spec/controllers`.
  #
  # You can disable this behaviour by removing the line below, and instead
  # explicitly tag your specs with their type, e.g.:
  #
  #     RSpec.describe UsersController, type: :controller do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://relishapp.com/rspec/rspec-rails/docs
  config.infer_spec_type_from_file_location!

  # Disable fixture support to avoid fixture_path= errors
  config.use_active_record = false

  # GraphQL test helper
  config.include Module.new {
    def execute_graphql(query, variables: {}, user: nil)
      user ||= create(:user)
      OccumedSchema.execute(query, variables: variables, context: { requester: user })
    end
  }, type: :graphql

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!
  # arbitrary gems may also be filtered via:
  # config.filter_gems_from_backtrace("gem name")
  Shoulda::Matchers.configure do |config|
    config.integrate do |with|
      with.test_framework :rspec
      with.library :rails
    end
  end
end
