module TestData
  module_function

  def image_data
    # Return a simple hash for custom Attachment model
    {
      "id" => "test_image_#{SecureRandom.hex(8)}",
      "storage" => "store",
      "metadata" => {
        "size" => File.size(Rails.root.join("spec/fixtures/files/image.jpeg")),
        "mime_type" => "image/jpeg",
        "filename" => "image.jpeg"
      }
    }
  end

  def pdf_data
    # Return a simple hash for custom Attachment model
    {
      "id" => "test_pdf_#{SecureRandom.hex(8)}",
      "storage" => "store",
      "metadata" => {
        "size" => File.size(Rails.root.join("spec/fixtures/files/sample.pdf")),
        "mime_type" => "application/pdf",
        "filename" => "sample.pdf"
      }
    }
  end

  def uploaded_image
    fixture_file_upload(Rails.root.join("spec/fixtures/files/image.jpeg"), "image/jpeg")
  end

  def uploaded_pdf
    fixture_file_upload(Rails.root.join("spec/fixtures/files/sample.pdf"), "application/pdf")
  end
end
