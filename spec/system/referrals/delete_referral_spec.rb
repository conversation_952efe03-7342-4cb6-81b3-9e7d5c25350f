require "rails_helper"

describe "Delete Referral" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "successfully deletes" do
    org_user = create(:organisation_user)
    sign_in org_user.user
    patient = create(:patient, organisation: org_user.organisation)
    referral = create(:referral, patient: patient)

    visit edit_referral_path(referral)

    click_link "Delete"

    expect(page).to have_text "Referral was successfully deleted"
  end
end
