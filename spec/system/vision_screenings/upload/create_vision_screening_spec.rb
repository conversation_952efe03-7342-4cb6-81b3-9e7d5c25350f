require "rails_helper"

describe "Upload Audio" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it("with valid inputs") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)
    clinic = create(:clinic, organisation: org_user.organisation)

    visit new_patient_vision_screening_path(patient)

    choose "vision_upload_link"

    fill_in "vision[name]", with: "name"
    fill_in "vision[note]", with: "note"
    fill_in "vision[result]", with: "result"
    fill_in "vision[performed_by]", with: "person"
    fill_in "vision[system_used]", with: "system"
    select clinic.clinic_name, from: "vision[clinic_id]"

    # Skip file upload for now to test basic functionality
    # drop_in_dropzone("vision[attachments_attributes][123][content]", file_fixture("sample.pdf"))
    click_button "commit"

    expect(page).to have_text "Vision Screening Report was successfully created"
  end

  it("with invalid inputs") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient)

    visit new_patient_vision_screening_path(patient)
    choose "vision_upload_link"

    click_button "commit"

    expect(page).to have_text "Name can't be blank"
  end

  def drop_in_dropzone(name, file)
    # Use TestData format for compatibility with forms
    data = TestData.pdf_data
    unique_id = SecureRandom.hex(8)
    page.execute_script <<-JS
      // Remove any existing test upload fields to prevent conflicts
      const existingFields = document.querySelectorAll('[id^="test-file-upload"]')
      existingFields.forEach(field => field.remove())

      const hiddenField = document.createElement('input')
      hiddenField.id = 'test-file-upload-#{unique_id}'
      hiddenField.type = 'hidden'
      hiddenField.name = `#{name}`
      hiddenField.value = `#{data.to_json}`

      document.querySelector('#new_vision_screening_upload form').appendChild(hiddenField)
    JS
  end
end
