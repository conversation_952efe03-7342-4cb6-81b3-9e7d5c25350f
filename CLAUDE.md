# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Commands
- `bundle install` - Install Ruby gems 
- `yarn install` - Install JavaScript dependencies
- `bin/setup` - Setup development environment (installs deps, prepares database)
- `bundle exec rails server` - Start Rails server (port 3000)
- `bundle exec foreman start -f Procfile.dev` - Start all services (Rails, Sidekiq, asset watchers)

### Testing
- `bundle exec rspec` - Run all tests
- `bundle exec rspec spec/models/` - Run model tests only
- `bundle exec rspec spec/system/` - Run system tests only
- System tests use Cuprite (Chrome headless) driver

### Asset Building
- `yarn build` - Build both JavaScript and CSS assets
- `yarn build:js` - Build JavaScript with esbuild
- `yarn build:css` - Build CSS with Tailwind
- For development, assets auto-rebuild via Procfile.dev watchers

### Database
- `bundle exec rails db:migrate` - Run pending migrations
- `bundle exec rails db:prepare` - Setup database (creates if needed, runs migrations)
- `bundle exec rails db:seed` - Seed development data

### Background Jobs
- `bundle exec sidekiq` - Start Sidekiq worker for background jobs
- Jobs are used for generating PDF reports (evaluations, lab tests, physicals, vision screenings)

## Architecture Overview

### Core Domain
This is an **occupational health management system** for medical clinics conducting workplace health assessments. Key entities:

- **Organisations** - Top-level tenant, contains clinics, teams, companies, patients
- **Patients** - Workers requiring medical evaluations, belongs to organisation
- **Companies** - Employers that patients work for
- **Employments** - Junction between patients and companies with job details
- **Evaluations** - Main medical assessment records combining multiple test types
- **Clinics** - Physical locations where assessments are performed
- **Teams** - Groups of users within an organisation

### Assessment Types
The system handles multiple types of medical assessments:
- **Lab Tests** - Blood work, drug screening, etc.
- **Audio Tests** (Audiometry) - Hearing assessments
- **Vision Screenings** - Eye tests, can be uploaded files or captured data
- **Physical Examinations** - General medical exams with vitals
- **Spiro Tests** - Lung function tests
- **ECG** - Heart monitoring
- **X-rays** - Chest X-rays for lung health
- **Heat Stress** and **Height** assessments for specific work environments

### Multi-tenancy
- Users belong to organisations via `organisation_users` join table
- Most models are scoped to organisations
- Default organisation_id is 1 for development

### Authentication & Authorization
- Uses Devise for authentication with invitation support
- User registration with organisation assignment
- Settings namespace for user preferences and signatures

### Report Generation
- PDF reports generated asynchronously via Sidekiq workers
- Uses Grover gem (Puppeteer) for HTML-to-PDF conversion
- Reports stored as ActiveStorage attachments
- Digital signatures supported for report sign-offs

### Frontend Stack
- **Rails 7.1** with Hotwire (Turbo + Stimulus)
- **StimulusReflex** for real-time interactivity (being phased out per README)
- **ViewComponent** for reusable UI components
- **Tailwind CSS** for styling
- **esbuild** for JavaScript bundling
- Form objects using Reform gem for complex forms

### Key Integrations
- **PostgreSQL** with pg_search for full-text search
- **Redis** for Sidekiq job queue
- **AWS S3** for file storage (configured for production)
- **Skylight** for performance monitoring
- **GraphQL** API available at `/graphql` (development only GraphiQL at `/graphiql`)

### Component Architecture
- Heavy use of ViewComponents for UI modularity
- Timeline components for displaying patient history
- Report components for PDF generation
- Form components for data capture

### File Organization
- Models follow standard Rails conventions with extensive annotations
- Controllers organized by namespace (admin, settings, capture)
- Components in `app/components/` with paired .rb/.html.erb files
- Workers in `app/workers/` for background job processing
- Dashboards in `app/dashboards/` for admin interface (Administrate gem)

### Search & Performance
- Uses PostgreSQL trigram search via pg_search
- Search scopes on Patient and Evaluation models
- Strong migrations gem prevents unsafe schema changes
- Bullet gem for N+1 query detection in development

### Testing Strategy
- RSpec for unit and integration testing
- FactoryBot for test data generation
- Capybara + Cuprite for system tests
- Shoulda matchers for model validations
- Mocha for stubbing/mocking