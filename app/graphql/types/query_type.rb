module Types
  class QueryType < Types::BaseObject
    # Add `node(id: ID!) and `nodes(ids: [ID!]!)`
    include GraphQL::Types::Relay::HasNodeField
    include GraphQL::Types::Relay::HasNodesField

    field :organisation, resolver: Queries::Organisation

    field :patient, resolver: Queries::Patient
    field :patients, resolver: Queries::Patients

    field :recent_patients, resolver: Queries::RecentPatients

    field :patient_documents, resolver: Queries::PatientDocuments
    field :patient_document, resolver: Queries::PatientDocument

    field :company, resolver: Queries::Company
    field :companies, resolver: Queries::Companies

    field :clinic, resolver: Queries::Clinic
    field :clinics, resolver: Queries::Clinics

    field :medical, resolver: Queries::Medical
    field :medicals, resolver: Queries::Medicals

    field :draft_medicals, resolver: Queries::DraftMedicals
    field :signed_off_medicals, resolver: Queries::SignedOffMedicals

    field :employment, resolver: Queries::Employment
    field :employments_by_patient, resolver: Queries::EmploymentsByPatient
    field :employments_by_company, resolver: Queries::EmploymentsByCompany

    field :audios, resolver: Queries::Audios
    field :audio, resolver: Queries::Audio

    field :lab_test, resolver: Queries::LabTest
    field :lab_tests, resolver: Queries::LabTests

    field :spiros, resolver: Queries::Spiros
    field :spiro, resolver: Queries::Spiro

    field :physicals, resolver: Queries::Physicals
    field :physical, resolver: Queries::Physical

    field :vision_screenings, resolver: Queries::VisionScreenings
    field :vision_screening, resolver: Queries::VisionScreening

    field :me, Types::UserType, null: false

    def me
      verify_requester!
      requester
    end
  end
end
