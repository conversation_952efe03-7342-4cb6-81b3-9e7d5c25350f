<%= turbo_frame_tag dom_id(document) do %>
  <%= form_with model: document, scope: "patient_document", url: url , data: {"turbo-frame" => "_top"}, local: true do |f| %>

    <% if document.errors.any? %>
      <div id="error_explanation" class="bg-red-50 text-red-800 p-8 mt-4">
        <h2 class='mb-2'><%= pluralize(document.errors.count, "error") %> prohibited this document from being saved:</h2>

        <ul class="list-disc ml-6">
          <% document.errors.full_messages.each do |message| %>
            <li class="list-disc"><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="">
      <div class="mt-6 sm:mt-5">
        <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <label for="about" class="block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
            Name
          </label>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <div class="max-w-lg flex rounded-md shadow-sm">
              <%= f.text_field :name, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"%>
            </div>
          </div>
        </div>
        <div class="mt-6 sm:mt-5">
          <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
            <label for="about" class="block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
              Description
            </label>
            <div class="mt-1 sm:mt-0 sm:col-span-2">
              <div class="max-w-lg flex rounded-md shadow-sm">
                <%= f.text_area :description, rows: 3, class: "form-textarea block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"%>
              </div>
              <p class="mt-2 text-sm text-gray-500">Describe the document to be attached</p>
            </div>
          </div>
        </div>

      <% if f.object.respond_to?(:attachments_attributes) && f.object.attachments_attributes.present? %>
        <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <label for="cover_photo" class="block text-sm leading-5 font-medium text-gray-700 sm:mt-px sm:pt-2">
            Uploaded Files
          </label>

          <div class="mt-2 sm:mt-0 sm:col-span-2">
            <div class="max-w-lg flex  w-full justify-center px-6 border-2 border-gray-300 border-dashed rounded-md">
              <%  f.object.attachments.each do |att| %>
                <div data-controller='remove'>
                  <div class="py-4 cursor-pointer" data-remove-target='removable'>
                    <span><%= JSON.parse(att.second['content'])['metadata']['filename'] %></span>
                    <span class="ml-4 hover:underline"
                          data-action='click->remove#remove'
                          >X</span>
                  </div>
                  <input
                    type="hidden"
                    data-remove-target='removable'
                    id="<%= att.first %>"
                    name="patient_document[attachments_attributes][<%= att.first %>][content]"
                    value="<%= att.second['content']%>"
                    >
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

      <%# f.fields_for :attachments do |pf| %>
        <%# render partial: "audios/attachment", locals: { attachment: pf.object, f: pf } %>
      <%# end %>

      <% if f.object.attachments.present?%>
        <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <label for="cover_photo" class="block text-sm leading-5 font-medium text-gray-700 sm:mt-px sm:pt-2">
            Uploaded Files
          </label>

          <div class="mt-2 sm:mt-0 sm:col-span-2">
            <div class="max-w-lg flex  w-full justify-center px-6 border-2 border-gray-300 border-dashed rounded-md">
              <%  f.object.attachments.each do |att| %>
                <% if !att.persisted? %>
                  <div data-controller='remove'>
                    <div class="py-4 cursor-pointer"
                         data-remove-target='removable'>
                      <span><%= JSON.parse(att.content)['metadata']['filename'] %></span>
                      <span class="ml-4 hover:underline"
                            data-action='click->remove#remove'
                            >X</span>
                    </div>
                    <input
                      type="hidden"
                      data-remove-target='removable'
                      id="<%= att.id %>"
                      name="patient_document[attachments_attributes][<%= JSON.parse(att.content)['id'] %>][content]"
                      value="<%= att.content %>"
                      >
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

        <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <label for="cover_photo" class="block text-sm leading-5 font-medium text-gray-700 sm:mt-px sm:pt-2">
           Attachment
          </label>
          <div class="mt-2 sm:mt-0 sm:col-span-2">
            <div class="max-w-lg flex  w-full justify-center px-6 border-2 border-gray-300 border-dashed rounded-md">
              <div class="text-center dropzone dz-clickable w-full"
                   data-dropzone-scope-value='patient_document[attachments_attributes]'
                   data-dropzone-field-value='content'
                   data-controller="dropzone">
                <%= f.file_field :attachments_attributes, multiple: true,
                  class: "form-textarea block w-full transition
                        duration-150 ease-in-out sm:text-sm sm:leading-5",
                        data: {target: "dropzone.input"} %>
                      <svg class="dz-clickable mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      <p class="dropzone-msg mt-1 text-sm text-gray-600">
                      <button type="button"
                              class="dropzone-msg-title font-medium text-indigo-600 hover:text-indigo-500
                                             focus:outline-none focus:underline transition duration-150 ease-in-out">
                        Upload a file
                      </button>
                      or drag and drop
                      </p>
                      <p class="dropzone-msg-desc mt-1 text-xs text-gray-500">
                      PNG, JPG, GIF up to 10MB
                      </p>
              </div>
            </div>
          </div>
        </div>

      <div class="flex justify-between items-center mt-8 border-t border-gray-200 pt-5">
        <div class="hover:text-indigo-400 text-gray-500">
          <% if document.persisted? %>
            <%= link_to "Delete", patient_document_path(document), method: "delete", data: { confirm: "Are you sure?", disable_with: "Processing..." } %>
          <% end %>
        </div>
        <div class="flex ">
          <span class="inline-flex rounded-md shadow-sm">
            <%= link_to "Cancel", :back,
              class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                   text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                   focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
          </span>
          <span class="ml-3 inline-flex rounded-md shadow-sm">
            <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
          </span>
        </div>
      </div>
    </div>
  <% end %>
<% end %>
