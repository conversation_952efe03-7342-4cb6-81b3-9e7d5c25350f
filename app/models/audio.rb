# == Schema Information
#
# Table name: audios
#
#  id             :bigint           not null, primary key
#  date_performed :date
#  name           :string
#  note           :text
#  performed_by   :string
#  result         :string
#  signature_date :date
#  signed_by      :text
#  status         :text
#  system_used    :text
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  clinic_id      :bigint
#  patient_id     :bigint
#
# Indexes
#
#  index_audios_on_clinic_id   (clinic_id)
#  index_audios_on_patient_id  (patient_id)
#
class Audio < ApplicationRecord
  belongs_to :patient
  belongs_to :clinic

  has_many_attached :attachments, dependent: :destroy

  # Virtual attributes for form compatibility
  attr_accessor :signed, :attachments_attributes

  validates :clinic_id, :patient_id, :name, presence: true
end
