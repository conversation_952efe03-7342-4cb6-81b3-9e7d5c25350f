source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "3.3.6"

gem "skylight"
gem "active_storage_validations"
gem "apollo_upload_server", "2.1"
gem "rails", "~> 7.1"
gem "pg", "~> 1.5.8"
gem "puma", "~> 4.1"
gem "bootsnap", ">= 1.4.2", require: false
gem "active_record-acts_as", "~> 4.0", ">= 4.0.3"
gem "inline_svg", "~> 1.7", ">= 1.7.1"
gem "grover", "~> 1.1.9"
gem "stimulus_reflex"
gem "aws-sdk-s3", "~> 1.14"
gem "view_component", "~> 2.49.0"
gem "cssbundling-rails"
gem "rack-cors"
gem "view_component_reflex" 
gem "reform-rails", "~> 0.2.3"
gem "reform", "~> 2.6", ">= 2.6.1"
gem "cable_ready"
gem "devise", "~> 4.8", ">= 4.8.1"
gem "name_of_person"
gem "sprockets-rails", "~> 3.4.2"
gem "pg_search"
gem "redis", ">= 4.0"
gem "hiredis"
gem "sidekiq"
gem "marcel", "~> 1.0", ">= 1.0.2"
gem "lograge", "~> 0.11.2"
gem "devise_invitable", "~> 2.0", ">= 2.0.6"
gem "strong_migrations"
gem "geared_pagination", "~> 1.0", ">= 1.0.1"
gem "turbo-rails", "= 0.5.4"
gem "jsbundling-rails"
gem "stimulus-rails"
gem "image_processing", ">= 1.2"

group :development do
  gem "annotate", github: "dabit/annotate_models", branch: "rails-7"
  gem "listen", "~> 3.2"
  gem "guard-livereload", "~> 2.5.2", require: false
  gem "rack-livereload"
  gem "better_errors"
  gem "binding_of_caller"
  gem "faker"
  gem "spring", "~> 3.0"
  gem "rails-erd"
  gem "rack-mini-profiler", "~> 2.3"
end

group :test do
  gem "capybara", ">= 2.15"
  gem "selenium-webdriver"
  gem "webdrivers"
  gem "shoulda", "~> 4.0"
  gem "rspec_junit_formatter"
  gem "rspec-rails", "~> 5.0", ">= 5.0.2"
  gem "factory_bot_rails", "~> 6.2"
  gem "cuprite"
  gem "mocha"
end

group :development, :test do
  gem "awesome_print"
  gem "pry-rails"
  gem "bullet"
  gem "byebug", platforms: %i[mri mingw x64_mingw]
  gem "graphiql-rails", git: "https://github.com/rmosolgo/graphiql-rails.git", branch: "master"
end

gem "tzinfo-data", platforms: %i[mingw mswin x64_mingw jruby]
gem "graphql-rails_logger"
gem "graphql", "~> 2.4.0"
gem "hotwire-rails", "~> 0.1.3"

gem "administrate", "~> 0.18.0"
