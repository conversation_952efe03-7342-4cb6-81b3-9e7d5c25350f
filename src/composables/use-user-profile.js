import { ref, computed } from 'vue';
import { useQuery, useMutation } from '@vue/apollo-composable';
import { useCacheFirstQuery } from './use-cache-first-query';
import gql from 'graphql-tag';

// GraphQL queries and mutations
const CURRENT_USER_QUERY = gql`
  query CurrentUser {
    currentUser {
      id
      email
      firstName
      lastName
      fullName
      profilePicture
      createdAt
      updatedAt
      organisationId
    }
  }
`;

const UPDATE_USER_PROFILE_MUTATION = gql`
  mutation UpdateUserProfile($input: UserProfileInput!) {
    updateUserProfile(input: $input) {
      success
      user {
        id
        email
        firstName
        lastName
        fullName
        profilePicture
        updatedAt
      }
      errors {
        path
        message
      }
    }
  }
`;

const CHANGE_PASSWORD_MUTATION = gql`
  mutation ChangePassword($input: ChangePasswordInput!) {
    changePassword(input: $input) {
      success
      message
      errors {
        path
        message
      }
    }
  }
`;

/**
 * Composable for user profile management
 * Provides reactive user data and methods for profile updates
 */
export function useUserProfile() {
  // Fetch current user with cache-first strategy
  const { 
    result: userResult, 
    loading: userLoading, 
    error: userError, 
    refetch: refetchUser 
  } = useCacheFirstQuery(CURRENT_USER_QUERY);

  // Computed user data
  const currentUser = computed(() => {
    return userResult.value?.currentUser || null;
  });

  // User profile update mutation
  const {
    mutate: updateUserProfile,
    loading: updateLoading,
    onDone: onUpdateDone,
    onError: onUpdateError
  } = useMutation(UPDATE_USER_PROFILE_MUTATION);

  // Password change mutation
  const {
    mutate: changePassword,
    loading: passwordLoading,
    onDone: onPasswordDone,
    onError: onPasswordError
  } = useMutation(CHANGE_PASSWORD_MUTATION);

  // State for UI feedback
  const updateSuccess = ref(false);
  const updateError = ref('');
  const passwordSuccess = ref(false);
  const passwordError = ref('');

  /**
   * Update user profile information
   * @param {Object} profileData - Profile data to update
   * @returns {Promise} - Mutation promise
   */
  const updateProfile = async (profileData) => {
    updateSuccess.value = false;
    updateError.value = '';

    try {
      const result = await updateUserProfile({
        input: profileData
      });

      if (result.data?.updateUserProfile?.success) {
        updateSuccess.value = true;
        // Refetch user data to update cache
        await refetchUser();
        return result.data.updateUserProfile.user;
      } else if (result.data?.updateUserProfile?.errors?.length > 0) {
        updateError.value = result.data.updateUserProfile.errors
          .map(error => error.message)
          .join(', ');
        throw new Error(updateError.value);
      }
    } catch (error) {
      updateError.value = error.message || 'Failed to update profile';
      throw error;
    }
  };

  /**
   * Change user password
   * @param {Object} passwordData - Password change data
   * @returns {Promise} - Mutation promise
   */
  const updatePassword = async (passwordData) => {
    passwordSuccess.value = false;
    passwordError.value = '';

    try {
      const result = await changePassword({
        input: passwordData
      });

      if (result.data?.changePassword?.success) {
        passwordSuccess.value = true;
        return result.data.changePassword.message || 'Password changed successfully';
      } else if (result.data?.changePassword?.errors?.length > 0) {
        passwordError.value = result.data.changePassword.errors
          .map(error => error.message)
          .join(', ');
        throw new Error(passwordError.value);
      }
    } catch (error) {
      passwordError.value = error.message || 'Failed to change password';
      throw error;
    }
  };

  /**
   * Clear success/error messages
   */
  const clearMessages = () => {
    updateSuccess.value = false;
    updateError.value = '';
    passwordSuccess.value = false;
    passwordError.value = '';
  };

  /**
   * Check if user is authenticated
   */
  const isAuthenticated = computed(() => {
    return !!currentUser.value;
  });

  /**
   * Get user's full name or fallback
   */
  const displayName = computed(() => {
    if (!currentUser.value) return '';
    
    if (currentUser.value.fullName) {
      return currentUser.value.fullName;
    }
    
    const firstName = currentUser.value.firstName || '';
    const lastName = currentUser.value.lastName || '';
    const fullName = `${firstName} ${lastName}`.trim();
    
    return fullName || currentUser.value.email || 'User';
  });

  /**
   * Get user's initials for avatar fallback
   */
  const userInitials = computed(() => {
    if (!currentUser.value) return '';
    
    const firstName = currentUser.value.firstName || '';
    const lastName = currentUser.value.lastName || '';
    
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }
    
    if (currentUser.value.email) {
      return currentUser.value.email.charAt(0).toUpperCase();
    }
    
    return 'U';
  });

  return {
    // User data
    currentUser,
    displayName,
    userInitials,
    isAuthenticated,
    
    // Loading states
    userLoading,
    updateLoading,
    passwordLoading,
    
    // Error states
    userError,
    updateError,
    passwordError,
    
    // Success states
    updateSuccess,
    passwordSuccess,
    
    // Methods
    updateProfile,
    updatePassword,
    refetchUser,
    clearMessages
  };
}

/**
 * Utility function to format user creation/update dates
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date
 */
export function formatUserDate(dateString) {
  if (!dateString) return 'Not available';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return 'Invalid date';
  }
}

/**
 * Utility function to validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} - Validation result with score and checks
 */
export function validatePasswordStrength(password) {
  if (!password) {
    return {
      score: 0,
      strength: '',
      checks: {
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false
      }
    };
  }

  const checks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    number: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  };

  const score = Object.values(checks).filter(Boolean).length;
  
  let strength = '';
  if (score <= 1) strength = 'Very Weak';
  else if (score === 2) strength = 'Weak';
  else if (score === 3) strength = 'Fair';
  else if (score === 4) strength = 'Good';
  else strength = 'Strong';

  return {
    score,
    strength,
    checks
  };
}
