<template>
  <q-page padding>
    <div>
      <div class="">
        <div class="flex flex-col max-w-4xl">
          <main class="flex-1">
            <div class="relative max-w-4xl md:px-8 xl:px-0">
              <div class="">
                <PageHeading class="pb-6">
                  <template #heading> Settings </template>

                  <template #buttons>
                    <button
                      class="px-4 py-2 text-stone-600 bg-stone-50 text-bold hover:bg-stone-100"
                      @click="signOut"
                    >
                      <div class="text-sm">Sign out</div>
                    </button>
                  </template>
                </PageHeading>

                <div class="px-4 sm:px-6 md:px-0">
                  <div class="py-6">
                    <!-- Tabs -->
                    <div class="md:hidden">
                      <label for="selected-tab" class="sr-only"
                        >Select a tab</label
                      >
                      <select
                        id="selected-tab"
                        name="selected-tab"
                        class="block w-full py-2 pl-3 pr-10 mt-1 text-base border-gray-300 rounded-md focus:border-stone-500 focus:outline-none focus:ring-stone-500 sm:text-sm"
                      >
                        <option
                          v-for="tab in tabs"
                          :key="tab.name"
                          :selected="tab.current"
                        >
                          {{ tab.name }}
                        </option>
                      </select>
                    </div>

                    <TabGroup>
                      <TabList
                        class="flex mb-px space-x-8 vk-hidden md:block"
                      >
                        <Tab as="template" v-slot="{ selected }">
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Profile
                          </button>
                        </Tab>

                        <Tab as="template" v-slot="{ selected }">
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Password
                          </button></Tab
                        >
                        <Tab as="template" v-slot="{ selected }">
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Signature
                          </button></Tab
                        >

                        <Tab as="template" v-slot="{ selected }">
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Organisation
                          </button></Tab
                        >

                        <Tab as="template" v-slot="{ selected }">
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Clinic
                          </button></Tab
                        >

                        <!-- Cache Tab (Development Only) -->
                        <Tab
                          v-if="isDevelopment"
                          as="template"
                          v-slot="{ selected }"
                        >
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Cache
                          </button></Tab
                        >
                      </TabList>
                      <TabPanels>
                        <TabPanel>
                          <ProfileTabPanel
                            v-if="!showProfileEdit"
                            @edit-profile="showProfileEdit = true"
                          />
                          <ProfileEditForm
                            v-else
                            :user="currentUser"
                            @cancel="showProfileEdit = false"
                            @success="handleProfileUpdateSuccess"
                          />
                        </TabPanel>
                        <TabPanel>
                          <PasswordChangeForm />
                        </TabPanel>
                        <TabPanel>Content 3</TabPanel>
                        <TabPanel>Content 4</TabPanel>
                        <TabPanel><ClinicTabPanel /></TabPanel>

                        <!-- Cache Tab Panel (Development Only) -->
                        <TabPanel v-if="isDevelopment">
                          <div class="w-full max-w-4xl">
                            <PageHeading
                              class="pb-6 border-b border-stone-400"
                            >
                              <template #heading
                                >Cache Diagnostics</template
                              >
                              <template #description>
                                Monitor and manage the Apollo GraphQL
                                cache
                              </template>
                            </PageHeading>

                            <div class="mt-8 space-y-6">
                              <!-- Cache Statistics Card -->
                              <div
                                class="overflow-hidden bg-white border shadow border-stone-200 sm:rounded-lg"
                              >
                                <div class="px-4 py-5 sm:px-6">
                                  <h3
                                    class="text-lg font-medium leading-6 text-gray-900"
                                  >
                                    Cache Statistics
                                  </h3>
                                  <p
                                    class="max-w-2xl mt-1 text-sm text-gray-500"
                                  >
                                    Current cache status and
                                    performance metrics
                                  </p>
                                </div>
                                <div
                                  class="px-4 py-5 border-t border-gray-200 sm:p-6"
                                >
                                  <!-- Cache Statistics -->
                                  <div
                                    v-if="cacheStats"
                                    class="space-y-6"
                                    data-testid="cache-stats"
                                  >
                                    <div
                                      class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4"
                                    >
                                      <div
                                        class="overflow-hidden border rounded-lg shadow-sm bg-stone-50 border-stone-200"
                                      >
                                        <div class="p-5">
                                          <div
                                            class="flex items-center"
                                          >
                                            <div class="hidden">
                                              <div
                                                class="flex items-center justify-center w-8 h-8 bg-teal-600 rounded-md"
                                              >
                                                <span
                                                  class="text-sm font-bold text-white"
                                                  >E</span
                                                >
                                              </div>
                                            </div>
                                            <div class="flex-1 w-0">
                                              <dl>
                                                <dt
                                                  class="text-sm font-medium truncate text-stone-500"
                                                >
                                                  Entities
                                                </dt>
                                                <dd
                                                  class="text-lg font-medium text-stone-900"
                                                >
                                                  {{
                                                    cacheStats.entityCount
                                                  }}
                                                </dd>
                                              </dl>
                                            </div>
                                          </div>
                                        </div>
                                      </div>

                                      <div
                                        class="overflow-hidden border rounded-lg shadow-sm bg-stone-50 border-stone-200"
                                      >
                                        <div class="p-5">
                                          <div
                                            class="flex items-center"
                                          >
                                            <div class="hidden">
                                              <div
                                                class="flex items-center justify-center w-8 h-8 bg-teal-600 rounded-md"
                                              >
                                                <span
                                                  class="text-sm font-bold text-white"
                                                  >S</span
                                                >
                                              </div>
                                            </div>
                                            <div class="flex-1 w-0">
                                              <dl>
                                                <dt
                                                  class="text-sm font-medium truncate text-stone-500"
                                                >
                                                  Size
                                                </dt>
                                                <dd
                                                  class="text-lg font-medium text-stone-900"
                                                >
                                                  {{
                                                    cacheStats.cacheSizeKB
                                                  }}KB
                                                </dd>
                                              </dl>
                                            </div>
                                          </div>
                                        </div>
                                      </div>

                                      <div
                                        class="overflow-hidden border rounded-lg shadow-sm bg-stone-50 border-stone-200"
                                      >
                                        <div class="p-5">
                                          <div
                                            class="flex items-center"
                                          >
                                            <div class="hidden">
                                              <div
                                                class="flex items-center justify-center w-8 h-8 bg-teal-600 rounded-md"
                                              >
                                                <span
                                                  class="text-sm font-bold text-white"
                                                  >Q</span
                                                >
                                              </div>
                                            </div>
                                            <div class="flex-1 w-0">
                                              <dl>
                                                <dt
                                                  class="text-sm font-medium truncate text-stone-500"
                                                >
                                                  Queries
                                                </dt>
                                                <dd
                                                  class="text-lg font-medium text-stone-900"
                                                >
                                                  {{
                                                    cacheStats.queryCount
                                                  }}
                                                </dd>
                                              </dl>
                                            </div>
                                          </div>
                                        </div>
                                      </div>

                                      <div
                                        class="overflow-hidden border rounded-lg shadow-sm bg-stone-50 border-stone-200"
                                      >
                                        <div class="p-5">
                                          <div
                                            class="flex items-center"
                                          >
                                            <div class="hidden">
                                              <div
                                                class="flex items-center justify-center w-8 h-8 bg-teal-600 rounded-md"
                                              >
                                                <span
                                                  class="text-sm font-bold text-white"
                                                  >P</span
                                                >
                                              </div>
                                            </div>
                                            <div class="flex-1 w-0">
                                              <dl>
                                                <dt
                                                  class="text-sm font-medium truncate text-stone-500"
                                                >
                                                  Persisted
                                                </dt>
                                                <dd
                                                  class="text-lg font-medium text-stone-900"
                                                >
                                                  {{
                                                    cacheStats.persistedSizeKB
                                                  }}KB
                                                </dd>
                                              </dl>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- Entity Types Breakdown -->
                                    <div
                                      v-if="cacheStats.entityTypes"
                                      class="mt-6"
                                    >
                                      <h4
                                        class="mb-3 text-sm font-medium text-stone-900"
                                      >
                                        Entity Types
                                      </h4>
                                      <div
                                        class="p-4 border rounded-lg bg-stone-50 border-stone-200"
                                      >
                                        <div
                                          class="grid grid-cols-2 gap-4 text-sm"
                                        >
                                          <div
                                            v-for="(
                                              count, type
                                            ) in cacheStats.entityTypes"
                                            :key="type"
                                            class="flex justify-between"
                                          >
                                            <span
                                              class="text-stone-600"
                                              >{{ type }}:</span
                                            >
                                            <span
                                              class="font-medium text-stone-900"
                                              >{{ count }}</span
                                            >
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- Last Updated -->
                                    <div
                                      class="mt-3 text-xs text-stone-500"
                                    >
                                      Last updated:
                                      {{
                                        formatTimestamp(
                                          cacheStats.timestamp,
                                        )
                                      }}
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- Cache Actions Card -->
                              <div
                                class="overflow-hidden bg-white border shadow border-stone-200 sm:rounded-lg"
                              >
                                <div class="px-4 py-5 sm:px-6">
                                  <h3
                                    class="text-lg font-medium leading-6 text-gray-900"
                                  >
                                    Cache Actions
                                  </h3>
                                  <p
                                    class="max-w-2xl mt-1 text-sm text-gray-500"
                                  >
                                    Manage cache state and diagnostics
                                  </p>
                                </div>
                                <div
                                  class="px-4 py-5 border-t border-gray-200 sm:p-6"
                                >
                                  <!-- Action Buttons -->
                                  <div class="flex flex-wrap gap-3">
                                    <button
                                      @click="refreshCacheStats"
                                      :disabled="loadingStats"
                                      class="inline-flex items-center px-4 py-2 text-sm font-medium bg-white border rounded-sm shadow-sm border-stone-300 text-stone-700 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 disabled:opacity-50"
                                    >
                                      <ArrowPathIcon
                                        class="w-4 h-4 mr-2 -ml-1"
                                        :class="{
                                          'animate-spin':
                                            loadingStats,
                                        }"
                                      />
                                      Refresh Stats
                                    </button>

                                    <button
                                      @click="validatePersistence"
                                      class="inline-flex items-center px-4 py-2 text-sm font-medium bg-white border rounded-sm shadow-sm border-stone-300 text-stone-700 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
                                    >
                                      Test Persistence
                                    </button>

                                    <button
                                      @click="toggleOfflineMode"
                                      :class="[
                                        'inline-flex items-center px-4 py-2 border text-sm font-medium rounded-sm focus:outline-none focus:ring-2 focus:ring-offset-2',
                                        isOfflineMode
                                          ? 'border-orange-300 text-orange-800 bg-orange-50 hover:bg-orange-100 focus:ring-orange-500'
                                          : 'border-stone-300 text-stone-700 bg-white hover:bg-stone-50 focus:ring-stone-500',
                                      ]"
                                    >
                                      <WifiIcon
                                        v-if="!isOfflineMode"
                                        class="w-4 h-4 mr-2 -ml-1"
                                      />
                                      <ExclamationTriangleIcon
                                        v-else
                                        class="w-4 h-4 mr-2 -ml-1"
                                      />
                                      {{
                                        isOfflineMode
                                          ? "Go Online"
                                          : "Go Offline"
                                      }}
                                    </button>

                                    <button
                                      @click="clearCache"
                                      class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                    >
                                      <TrashIcon
                                        class="w-4 h-4 mr-2 -ml-1"
                                      />
                                      Clear Cache
                                    </button>
                                  </div>

                                  <!-- Offline Mode Status -->
                                  <div
                                    v-if="isOfflineMode"
                                    class="p-3 mt-4 border border-orange-200 rounded-sm bg-orange-50"
                                  >
                                    <div class="flex">
                                      <ExclamationTriangleIcon
                                        class="w-5 h-5 text-orange-400"
                                      />
                                      <div class="ml-3">
                                        <h3
                                          class="text-sm font-medium text-orange-800"
                                        >
                                          Offline Mode Active
                                        </h3>
                                        <div
                                          class="mt-1 text-sm text-orange-700"
                                        >
                                          <p>
                                            Network requests are being
                                            blocked to simulate
                                            offline conditions.
                                            <span
                                              v-if="
                                                formattedOfflineDuration
                                              "
                                            >
                                              Offline for
                                              {{
                                                formattedOfflineDuration
                                              }}.
                                            </span>
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </TabPanel>
                      </TabPanels>
                    </TabGroup>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { ref, onMounted, computed } from "vue";
import {
  getCacheStats,
  clearCache as clearCacheUtil,
  validateCachePersistence,
} from "src/utils/apollo-cache-debug";
import {
  ArrowPathIcon,
  TrashIcon,
  WifiIcon,
  ExclamationTriangleIcon,
} from "@heroicons/vue/24/outline";
import { useApolloClient } from "@vue/apollo-composable";
import { useOfflineMode } from "src/composables/use-offline-mode";
import {
  TabGroup,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
} from "@headlessui/vue";
import ProfileTabPanel from "src/components/ProfileTabPanel.vue";
import ProfileEditForm from "src/components/ProfileEditForm.vue";
import PasswordChangeForm from "src/components/PasswordChangeForm.vue";
import PageHeading from "src/components/PageHeading.vue";

const router = useRouter();
const $q = useQuasar();

// Profile management state
const showProfileEdit = ref(false);
const currentUser = ref(null);

// Cache diagnostics state
const isDevelopment = computed(() => process.env.DEV);

// Tab configuration for mobile dropdown
const tabs = computed(() => {
  const baseTabs = [
    { name: "Profile", current: false },
    { name: "Password", current: false },
    { name: "Signature", current: false },
    { name: "Organisation", current: false },
    { name: "Clinic", current: false },
  ];

  if (isDevelopment.value) {
    baseTabs.push({ name: "Cache", current: false });
  }

  return baseTabs;
});
const cacheStats = ref(null);
const loadingStats = ref(false);
const cacheMessage = ref("");
const cacheMessageType = ref("success");

const { client } = useApolloClient();

// Offline mode functionality
const {
  isOfflineMode,
  formattedOfflineDuration,
  toggleOfflineMode: toggleOffline,
} = useOfflineMode();

// Wrapper for offline toggle with user feedback
const toggleOfflineMode = () => {
  toggleOffline();

  // Show feedback message
  if (isOfflineMode.value) {
    cacheMessage.value =
      "Offline mode activated - network requests will be blocked";
    cacheMessageType.value = "warning";
  } else {
    cacheMessage.value =
      "Online mode restored - network requests will work normally";
    cacheMessageType.value = "success";
  }

  // Clear message after 3 seconds
  setTimeout(() => {
    cacheMessage.value = "";
  }, 3000);
};

// Format timestamp for display
const formatTimestamp = (timestamp) => {
  return new Date(timestamp).toLocaleString();
};

// Refresh cache statistics
const refreshCacheStats = async () => {
  loadingStats.value = true;
  cacheMessage.value = "";

  try {
    const stats = await getCacheStats(client.cache);
    cacheStats.value = stats;

    if (stats.error) {
      cacheMessage.value = `Error getting cache stats: ${stats.error}`;
      cacheMessageType.value = "error";
    }
  } catch (error) {
    console.error("[Settings] Failed to get cache stats:", error);
    cacheMessage.value = `Failed to get cache stats: ${error.message}`;
    cacheMessageType.value = "error";
  } finally {
    loadingStats.value = false;
  }
};

// Validate cache persistence
const validatePersistence = async () => {
  cacheMessage.value = "";

  try {
    const result = await validateCachePersistence();

    if (result.success) {
      cacheMessage.value = `${result.message} (${result.latency}ms)`;
      cacheMessageType.value = "success";
    } else {
      cacheMessage.value = result.message;
      cacheMessageType.value = "error";
    }
  } catch (error) {
    console.error(
      "[Settings] Failed to validate persistence:",
      error,
    );
    cacheMessage.value = `Persistence validation failed: ${error.message}`;
    cacheMessageType.value = "error";
  }
};

// Clear cache with confirmation
const clearCache = async () => {
  if (
    !confirm(
      "Are you sure you want to clear the cache? This will remove all cached data and require fresh data loading.",
    )
  ) {
    return;
  }

  cacheMessage.value = "";

  try {
    const success = await clearCacheUtil(client.cache);

    if (success) {
      cacheMessage.value = "Cache cleared successfully";
      cacheMessageType.value = "success";

      // Refresh stats to show empty cache
      await refreshCacheStats();
    } else {
      cacheMessage.value = "Failed to clear cache";
      cacheMessageType.value = "error";
    }
  } catch (error) {
    console.error("[Settings] Failed to clear cache:", error);
    cacheMessage.value = `Failed to clear cache: ${error.message}`;
    cacheMessageType.value = "error";
  }
};

// Load initial cache stats on mount (dev only)
onMounted(() => {
  if (isDevelopment.value) {
    refreshCacheStats();
  }
});

// Profile management functions
const handleProfileUpdateSuccess = (updatedUser) => {
  currentUser.value = updatedUser;
  showProfileEdit.value = false;
};

function signOut(e) {
  $q.cookies.remove("occusolve-token");
  router.push("/login");
}
</script>
