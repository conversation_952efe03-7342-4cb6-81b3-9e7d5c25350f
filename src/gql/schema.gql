"""
An ISO 8601-encoded date
"""
scalar ISO8601Date

"""
An ISO 8601-encoded datetime
"""
scalar ISO8601DateTime

type Mutation {
  """
  Updates a patient by id
  """
  patientUpdate(id: ID!, input: PatientInput!): PatientUpdatePayload

  """
  Updates user profile information
  """
  updateUserProfile(
    input: UserProfileInput!
  ): UserProfileUpdatePayload

  """
  Changes user password
  """
  changePassword(input: ChangePasswordInput!): ChangePasswordPayload

  """
  Login mutation
  """
  login(email: String!, password: String!): LoginPayload

  """
  An example field added by the generator
  """
  testField: String!
}

type Patient {
  createdAt: ISO8601DateTime!
  dob: ISO8601Date
  email: String
  firstName: String
  gender: String
  id: ID!
  identificationNumber: String
  lastName: String
  organisationId: Int!
  phoneNumber: String
  updatedAt: ISO8601DateTime!
}

input PatientInput {
  dob: ISO8601Date
  email: String
  firstName: String
  gender: String
  identificationNumber: String
  lastName: String
  organisationId: Int
  phoneNumber: String
}

"""
Autogenerated return type of PatientUpdate
"""
type PatientUpdatePayload {
  errors: [UserError!]!
  patient: Patient
}

type Query {
  """
  Find an patient by ID
  """
  patient(id: ID!): Patient!

  """
  Find all patient
  """
  patients: [Patient!]!

  """
  Get current user profile
  """
  currentUser: User

  """
  Get user by ID
  """
  user(id: ID!): User
}

"""
A user-readable error
"""
type UserError {
  """
  A description of the error
  """
  message: String!

  """
  Which input value this error came from
  """
  path: [String!]
}

"""
User type for authentication and profile management
"""
type User {
  id: ID!
  email: String!
  firstName: String
  lastName: String
  fullName: String
  profilePicture: String
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!
  organisationId: Int!
}

"""
Input for updating user profile
"""
input UserProfileInput {
  firstName: String
  lastName: String
  email: String
  profilePicture: String
}

"""
Input for changing password
"""
input ChangePasswordInput {
  currentPassword: String!
  newPassword: String!
  confirmPassword: String!
}

"""
Autogenerated return type of UserProfileUpdate
"""
type UserProfileUpdatePayload {
  errors: [UserError!]!
  user: User
  success: Boolean!
}

"""
Autogenerated return type of ChangePassword
"""
type ChangePasswordPayload {
  errors: [UserError!]!
  success: Boolean!
  message: String
}

"""
Login payload
"""
type LoginPayload {
  token: String
  id: ID
  user: User
  errors: [UserError!]
}
