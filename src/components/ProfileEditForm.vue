<template>
  <SettingsTabPanel
    title="Edit Profile"
    description="Update your personal information and profile settings"
  >
    <SettingsFormCard
      title="Profile Information"
      description="Update your basic profile information"
      :loading="loading"
      :success-message="successMessage"
      :error-message="errorMessage"
      :show-cancel="true"
      submit-text="Save Changes"
      cancel-text="Cancel"
      @submit="submitForm"
      @cancel="$emit('cancel')"
    >
      <!-- Profile Picture Section -->
      <div class="col-span-1 mb-8">
        <div class="flex flex-col items-start space-y-4">
          <div class="flex-shrink-0">
            <div
              class="flex items-center justify-center w-20 h-20 overflow-hidden rounded-full bg-stone-300"
            >
              <img
                v-if="profilePicturePreview || user?.profilePicture"
                :src="profilePicturePreview || user?.profilePicture"
                :alt="'Profile picture preview'"
                class="object-cover w-20 h-20 rounded-full"
              />
              <UserIcon v-else class="w-12 h-12 text-stone-400" />
            </div>
          </div>
        </div>
      </div>
      <div class="col-span-2 mb-8">
        <div class="flex flex-col items-start space-y-4">
          <div class="flex flex-col items-start">
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              @change="handleFileChange"
              class="hidden"
            />
            <button
              type="button"
              @click="$refs.fileInput.click()"
              class="inline-flex items-center px-4 py-2 text-sm font-medium bg-white border rounded-md shadow-sm border-stone-300 text-stone-700 hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
            >
              Change photo
            </button>
            <p class="mt-2 text-xs text-stone-500">
              Maximum file size is 5MB
            </p>
          </div>
        </div>
      </div>
      <!-- Form Fields -->
      <SettingsFormField
        v-model="formData.firstName"
        type="text"
        name="firstName"
        label="First Name"
        placeholder="Enter your first name"
        required
        :errors="v$.firstName.$errors.map((e) => e.$message)"
        :col-span="5"
        @blur="v$.firstName.$touch"
      />

      <SettingsFormField
        v-model="formData.lastName"
        type="text"
        name="lastName"
        label="Last Name"
        placeholder="Enter your last name"
        required
        :errors="v$.lastName.$errors.map((e) => e.$message)"
        :col-span="5"
        @blur="v$.lastName.$touch"
      />

      <SettingsFormField
        v-model="formData.email"
        type="email"
        name="email"
        label="Email Address"
        placeholder="Enter your email address"
        required
        :errors="v$.email.$errors.map((e) => e.$message)"
        :col-span="6"
        @blur="v$.email.$touch"
      />
    </SettingsFormCard>
  </SettingsTabPanel>
</template>

<script setup>
import { reactive, ref, watch } from "vue";
import { CheckIcon, UserIcon } from "@heroicons/vue/24/outline";
import useVuelidate from "@vuelidate/core";
import { required, email, helpers } from "@vuelidate/validators";
import { useUserProfile } from "src/composables/use-user-profile";
import SettingsTabPanel from "src/components/SettingsTabPanel.vue";
import SettingsFormCard from "src/components/SettingsFormCard.vue";
import SettingsFormField from "src/components/SettingsFormField.vue";

// Props
const props = defineProps({
  user: {
    type: Object,
    default: null,
  },
});

// Emits
const emit = defineEmits(["cancel", "success"]);

// Form data
const formData = reactive({
  firstName: "",
  lastName: "",
  email: "",
  profilePicture: "",
});

// Reactive refs
const profilePicturePreview = ref("");
const successMessage = ref("");
const errorMessage = ref("");

// Validation rules
const rules = {
  firstName: {
    required: helpers.withMessage(
      "First name is required.",
      required,
    ),
  },
  lastName: {
    required: helpers.withMessage("Last name is required.", required),
  },
  email: {
    required: helpers.withMessage("Email is required.", required),
    email: helpers.withMessage(
      "Please enter a valid email address.",
      email,
    ),
  },
};

const v$ = useVuelidate(rules, formData);

// Use user profile composable
const {
  updateProfile,
  updateLoading: loading,
  updateSuccess,
  updateError,
  clearMessages,
} = useUserProfile();

// Watch for user prop changes to populate form
watch(
  () => props.user,
  (newUser) => {
    if (newUser) {
      formData.firstName = newUser.firstName || "";
      formData.lastName = newUser.lastName || "";
      formData.email = newUser.email || "";
      formData.profilePicture = newUser.profilePicture || "";
    }
  },
  { immediate: true },
);

// Handle file upload
const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    // Validate file size (1MB max)
    if (file.size > 5 * 1024 * 1024) {
      errorMessage.value = "File size must be less than 5MB";
      return;
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      errorMessage.value = "Please select a valid image file";
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      profilePicturePreview.value = e.target.result;
      formData.profilePicture = e.target.result;
    };
    reader.readAsDataURL(file);

    errorMessage.value = "";
  }
};

// Form submission
const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;

  clearMessages();
  errorMessage.value = "";
  successMessage.value = "";

  try {
    const updatedUser = await updateProfile({
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      profilePicture: formData.profilePicture,
    });

    successMessage.value = "Profile updated successfully!";
    emit("success", updatedUser);

    // Clear success message after 3 seconds
    setTimeout(() => {
      successMessage.value = "";
    }, 3000);
  } catch (error) {
    console.error("Profile update error:", error);
    errorMessage.value =
      updateError.value ||
      "An error occurred while updating your profile. Please try again.";
  }
};
</script>
