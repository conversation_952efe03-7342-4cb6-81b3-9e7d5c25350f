<template>
  <div :class="fieldClasses">
    <!-- Label -->
    <label
      v-if="label"
      :for="fieldId"
      class="block text-sm font-medium text-stone-600 mb-1"
    >
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- Description -->
    <p v-if="description" class="text-sm text-gray-500 mb-2">
      {{ description }}
    </p>

    <!-- Input Field -->
    <div class="relative">
      <!-- Text Input -->
      <input
        v-if="type === 'text' || type === 'email' || type === 'tel' || type === 'url'"
        :id="fieldId"
        :type="type"
        :name="name"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :autocomplete="autocomplete"
        :class="inputClasses"
        @input="onInput"
        @blur="onBlur"
        @focus="onFocus"
      />

      <!-- Password Input -->
      <input
        v-else-if="type === 'password'"
        :id="fieldId"
        type="password"
        :name="name"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :autocomplete="autocomplete"
        :class="inputClasses"
        @input="onInput"
        @blur="onBlur"
        @focus="onFocus"
      />

      <!-- Textarea -->
      <textarea
        v-else-if="type === 'textarea'"
        :id="fieldId"
        :name="name"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :rows="rows"
        :class="inputClasses"
        @input="onInput"
        @blur="onBlur"
        @focus="onFocus"
      ></textarea>

      <!-- Select -->
      <select
        v-else-if="type === 'select'"
        :id="fieldId"
        :name="name"
        :value="modelValue"
        :disabled="disabled"
        :class="inputClasses"
        @change="onChange"
        @blur="onBlur"
        @focus="onFocus"
      >
        <option v-if="placeholder" value="" disabled>{{ placeholder }}</option>
        <option
          v-for="option in options"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </option>
      </select>

      <!-- File Input -->
      <input
        v-else-if="type === 'file'"
        :id="fieldId"
        type="file"
        :name="name"
        :disabled="disabled"
        :accept="accept"
        :multiple="multiple"
        :class="fileInputClasses"
        @change="onFileChange"
        @blur="onBlur"
        @focus="onFocus"
      />

      <!-- Checkbox -->
      <div v-else-if="type === 'checkbox'" class="flex items-center">
        <input
          :id="fieldId"
          type="checkbox"
          :name="name"
          :checked="modelValue"
          :disabled="disabled"
          class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
          @change="onCheckboxChange"
          @blur="onBlur"
          @focus="onFocus"
        />
        <label v-if="checkboxLabel" :for="fieldId" class="ml-2 block text-sm text-gray-900">
          {{ checkboxLabel }}
        </label>
      </div>
    </div>

    <!-- Error Messages -->
    <div v-if="errors && errors.length > 0" class="mt-1">
      <p
        v-for="error in errors"
        :key="error"
        class="text-sm text-red-600"
      >
        {{ error }}
      </p>
    </div>

    <!-- Help Text -->
    <p v-if="helpText" class="mt-1 text-sm text-gray-500">
      {{ helpText }}
    </p>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

// Props
const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean, File, Array],
    default: '',
  },
  type: {
    type: String,
    default: 'text',
    validator: (value) => [
      'text', 'email', 'password', 'tel', 'url', 'textarea', 
      'select', 'file', 'checkbox'
    ].includes(value),
  },
  label: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    required: true,
  },
  placeholder: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  helpText: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  errors: {
    type: Array,
    default: () => [],
  },
  autocomplete: {
    type: String,
    default: 'off',
  },
  rows: {
    type: Number,
    default: 3,
  },
  options: {
    type: Array,
    default: () => [],
  },
  accept: {
    type: String,
    default: '',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  checkboxLabel: {
    type: String,
    default: '',
  },
  colSpan: {
    type: Number,
    default: 6,
    validator: (value) => [1, 2, 3, 4, 5, 6].includes(value),
  },
});

// Emits
const emit = defineEmits(['update:modelValue', 'blur', 'focus', 'change']);

// Computed
const fieldId = computed(() => `field-${props.name}`);

const fieldClasses = computed(() => {
  const spanClass = `sm:col-span-${props.colSpan}`;
  return spanClass;
});

const hasErrors = computed(() => props.errors && props.errors.length > 0);

const inputClasses = computed(() => {
  const baseClasses = [
    'block w-full rounded-sm shadow-sm sm:text-sm',
    'focus:ring-stone-500 focus:border-stone-500',
    'border-stone-300 text-stone-800'
  ];

  if (hasErrors.value) {
    baseClasses.push('border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500');
  }

  if (props.disabled) {
    baseClasses.push('bg-gray-50 text-gray-500 cursor-not-allowed');
  }

  return baseClasses.join(' ');
});

const fileInputClasses = computed(() => {
  return 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-sm file:border-0 file:text-sm file:font-medium file:bg-teal-50 file:text-teal-700 hover:file:bg-teal-100';
});

// Methods
const onInput = (event) => {
  emit('update:modelValue', event.target.value);
};

const onChange = (event) => {
  emit('update:modelValue', event.target.value);
  emit('change', event.target.value);
};

const onCheckboxChange = (event) => {
  emit('update:modelValue', event.target.checked);
  emit('change', event.target.checked);
};

const onFileChange = (event) => {
  const files = event.target.files;
  emit('update:modelValue', props.multiple ? Array.from(files) : files[0]);
  emit('change', props.multiple ? Array.from(files) : files[0]);
};

const onBlur = (event) => {
  emit('blur', event);
};

const onFocus = (event) => {
  emit('focus', event);
};
</script>
