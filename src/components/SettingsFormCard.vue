<template>
  <div
    class="overflow-hidden bg-white border shadow border-stone-200 sm:rounded-lg"
  >
    <!-- Form Header -->
    <div
      v-if="title || description || $slots.header"
      class="px-4 py-5 sm:px-6"
    >
      <div v-if="$slots.header">
        <slot name="header"></slot>
      </div>
      <div v-else>
        <h3 class="text-lg font-medium leading-6 text-gray-900">
          {{ title }}
        </h3>
        <p
          v-if="description"
          class="max-w-2xl mt-1 text-sm text-gray-500"
        >
          {{ description }}
        </p>
      </div>
    </div>

    <!-- Form Content -->
    <div class="border-t border-gray-200">
      <form @submit.prevent="onSubmit" class="space-y-6">
        <!-- Success Message -->
        <div
          v-if="successMessage"
          class="p-4 mx-4 mt-4 border border-green-200 rounded-md sm:mx-6 bg-green-50"
        >
          <div class="flex">
            <CheckCircleIcon class="w-5 h-5 text-green-400" />
            <div class="ml-3">
              <p class="text-sm font-medium text-green-800">
                {{ successMessage }}
              </p>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div
          v-if="errorMessage"
          class="p-4 mx-4 mt-4 border border-red-200 rounded-md sm:mx-6 bg-red-50"
        >
          <div class="flex">
            <XCircleIcon class="w-5 h-5 text-red-400" />
            <div class="ml-3">
              <p class="text-sm font-medium text-red-800">
                {{ errorMessage }}
              </p>
            </div>
          </div>
        </div>

        <!-- Form Fields -->
        <div class="px-4 py-5 sm:p-6">
          <div
            class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6"
          >
            <slot></slot>
          </div>
        </div>

        <!-- Form Actions -->
        <div
          v-if="showActions"
          class="flex justify-end px-4 py-4 space-x-3 sm:px-6 bg-stone-50"
        >
          <button
            v-if="showCancel"
            type="button"
            @click="onCancel"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
          >
            {{ cancelText }}
          </button>
          <button
            type="submit"
            :disabled="loading || disabled"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-teal-700 border border-transparent rounded-md hover:bg-teal-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowPathIcon
              v-if="loading"
              class="w-4 h-4 mr-2 animate-spin"
            />
            {{ loading ? loadingText : submitText }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import {
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
} from "@heroicons/vue/24/outline";

// Props
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  description: {
    type: String,
    default: "",
  },
  loading: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  successMessage: {
    type: String,
    default: "",
  },
  errorMessage: {
    type: String,
    default: "",
  },
  showActions: {
    type: Boolean,
    default: true,
  },
  showCancel: {
    type: Boolean,
    default: false,
  },
  submitText: {
    type: String,
    default: "Save Changes",
  },
  cancelText: {
    type: String,
    default: "Cancel",
  },
  loadingText: {
    type: String,
    default: "Saving...",
  },
});

// Emits
const emit = defineEmits(["submit", "cancel"]);

// Methods
const onSubmit = () => {
  emit("submit");
};

const onCancel = () => {
  emit("cancel");
};
</script>
