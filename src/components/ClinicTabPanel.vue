<template>
  <SettingsTabPanel
    title="Clinic Management"
    description="Listing of the registered clinics for this organisation"
    :loading="loading"
    :error="error"
    :empty="result && clinics && clinicCount == 0"
    empty-message="No clinics registered"
  >
    <template #actions>
      <q-btn
        flat
        class="text-white bg-teal-700 text-bold"
        padding="md"
        no-caps
        to="/clinics/new"
      >
        <PlusSmallIcon class="w-5 h-5 mr-1 text-bold" />
        <div class="text-sm">Add Clinic</div>
      </q-btn>
    </template>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-stone-200">
          <thead class="bg-stone-50">
            <tr>
              <th
                scope="col"
                class="py-3 pl-4 pr-3 text-xs font-medium tracking-wide text-left text-stone-500 uppercase sm:pl-6"
              >
                Clinic Name
              </th>
              <th
                scope="col"
                class="px-3 py-3 text-xs font-medium tracking-wide text-left text-stone-500 uppercase"
              >
                Address
              </th>
              <th
                scope="col"
                class="px-3 py-3 text-xs font-medium tracking-wide text-left text-stone-500 uppercase"
              >
                Phone
              </th>
              <th scope="col" class="relative py-3 pl-3 pr-4 sm:pr-6">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <tr
              v-for="clinic in clinics"
              :key="clinic.id"
              class="hover:bg-stone-50"
            >
              <td
                class="py-4 pl-4 pr-3 text-sm font-medium whitespace-nowrap text-stone-900 sm:pl-6"
              >
                {{ clinic.clinicName }}
              </td>
              <td class="px-3 py-4 text-sm text-stone-500">
                <div>{{ clinic.physicalAddress }}</div>
                <div v-if="clinic.physicalAddress2">
                  {{ clinic.physicalAddress2 }}
                </div>
              </td>
              <td
                class="px-3 py-4 text-sm whitespace-nowrap text-stone-500"
              >
                {{ clinic.phoneNumber }}
              </td>
              <td
                class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6"
              >
                <router-link
                  :to="{
                    name: 'edit_clinic',
                    params: { id: clinic.id },
                  }"
                  class="text-teal-600 hover:text-teal-900"
                >
                  <PencilIcon class="w-5 h-5" />
                  <span class="sr-only"
                    >Edit {{ clinic.clinicName }}</span
                  >
                </router-link>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </SettingsTabPanel>
</template>
<script setup>
import { PlusSmallIcon, PencilIcon } from "@heroicons/vue/24/outline";
import gql from "graphql-tag";
import { useCacheFirstQuery } from "src/composables/use-cache-first-query";
import { computed } from "vue";
import SettingsTabPanel from "src/components/SettingsTabPanel.vue";

const clinicCount = computed(() => {
  return result.value?.clinics?.length || 0;
});

const clinics = computed(() => {
  return result.value?.clinics || [];
});

const { result, error, loading } = useCacheFirstQuery(
  gql`
    query Clinics($id: ID!) {
      clinics(organisationId: $id) {
        id
        clinicName
        physicalAddress
        physicalAddress2
        phoneNumber
      }
    }
  `,
  {
    id: 1,
  },
);
</script>
