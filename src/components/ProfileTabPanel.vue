<template>
  <SettingsTabPanel
    title="Profile Information"
    description="View and manage your account details"
    :loading="loading"
    :error="error"
    :has-data="!!user"
    loading-text="Loading profile..."
    error-title="Error loading profile"
    :on-retry="refetch"
  >
    <template #actions>
      <q-btn
        flat
        class="text-white bg-teal-700 text-bold"
        padding="md"
        no-caps
        @click="$emit('edit-profile')"
      >
        <PencilIcon class="w-5 h-5 mr-1 text-bold" />
        <div class="text-sm">Edit Profile</div>
      </q-btn>
    </template>

    <!-- Profile Card -->
    <div
      class="bg-white shadow overflow-hidden border border-stone-200 sm:rounded-lg"
    >
      <div class="px-4 py-5 sm:px-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="h-20 w-20 rounded-full bg-stone-300 flex items-center justify-center"
            >
              <img
                v-if="user.profilePicture"
                :src="user.profilePicture"
                :alt="`${
                  user.fullName || user.email
                } profile picture`"
                class="h-20 w-20 rounded-full object-cover"
              />
              <UserIcon v-else class="h-12 w-12 text-stone-400" />
            </div>
          </div>
          <div class="ml-6">
            <h3 class="text-lg leading-6 font-medium text-stone-900">
              {{
                user.fullName ||
                `${user.firstName || ""} ${
                  user.lastName || ""
                }`.trim() ||
                "No name set"
              }}
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-stone-500">
              {{ user.email }}
            </p>
          </div>
        </div>
      </div>

      <div class="border-t border-stone-200">
        <dl>
          <div
            class="bg-stone-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
          >
            <dt class="text-sm font-medium text-stone-500">
              Full name
            </dt>
            <dd
              class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2"
            >
              {{
                user.fullName ||
                `${user.firstName || ""} ${
                  user.lastName || ""
                }`.trim() ||
                "Not provided"
              }}
            </dd>
          </div>

          <div
            class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
          >
            <dt class="text-sm font-medium text-stone-500">
              First name
            </dt>
            <dd
              class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2"
            >
              {{ user.firstName || "Not provided" }}
            </dd>
          </div>

          <div
            class="bg-stone-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
          >
            <dt class="text-sm font-medium text-stone-500">
              Last name
            </dt>
            <dd
              class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2"
            >
              {{ user.lastName || "Not provided" }}
            </dd>
          </div>

          <div
            class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
          >
            <dt class="text-sm font-medium text-stone-500">
              Email address
            </dt>
            <dd
              class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2"
            >
              {{ user.email || "Not provided" }}
            </dd>
          </div>

          <div
            class="bg-stone-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
          >
            <dt class="text-sm font-medium text-stone-500">
              Account created
            </dt>
            <dd
              class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2"
            >
              {{ formatDate(user.createdAt) }}
            </dd>
          </div>

          <div
            class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
          >
            <dt class="text-sm font-medium text-stone-500">
              Last updated
            </dt>
            <dd
              class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2"
            >
              {{ formatDate(user.updatedAt) }}
            </dd>
          </div>

          <div
            v-if="user.organisationId"
            class="bg-stone-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
          >
            <dt class="text-sm font-medium text-stone-500">
              Organisation
            </dt>
            <dd
              class="mt-1 text-sm text-stone-900 sm:mt-0 sm:col-span-2"
            >
              Organisation ID: {{ user.organisationId }}
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </SettingsTabPanel>
</template>

<script setup>
import { PencilIcon, UserIcon } from "@heroicons/vue/24/outline";
import {
  useUserProfile,
  formatUserDate,
} from "src/composables/use-user-profile";
import SettingsTabPanel from "src/components/SettingsTabPanel.vue";

// Define emits
const emit = defineEmits(["edit-profile"]);

// Use user profile composable
const {
  currentUser: user,
  userLoading: loading,
  userError: error,
  refetchUser: refetch,
} = useUserProfile();

// Helper function to format dates
const formatDate = formatUserDate;
</script>
