<template>
  <div class="flex content-center justify-center">
    <!-- Loading State -->
    <div v-if="loading && !hasData" class="w-full">
      <div class="text-center py-8">
        <div class="text-stone-500">
          {{ loadingText || "Loading..." }}
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="w-full">
      <div class="text-center py-8">
        <div class="text-red-600 text-lg font-medium">
          {{ errorTitle || "Error loading data" }}
        </div>
        <div class="text-stone-500 mt-2">
          {{ error.message || error }}
        </div>
        <button
          v-if="onRetry"
          @click="onRetry"
          class="mt-4 px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
        >
          Try Again
        </button>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="showEmptyStateComputed" class="w-full">
      <div class="text-center py-8">
        <div class="text-stone-500 text-lg font-medium">
          {{ emptyTitle || "No data available" }}
        </div>
        <div class="text-stone-400 mt-2">
          {{ emptyDescription || "There is no data to display." }}
        </div>
        <button
          v-if="emptyAction && emptyActionText"
          @click="emptyAction"
          class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
        >
          {{ emptyActionText }}
        </button>
      </div>
    </div>

    <!-- Content -->
    <div v-else class="w-full max-w-4xl">
      <!-- Page Heading -->
      <PageHeading class="pb-6 border-b border-stone-400">
        <template #heading>{{ title }}</template>
        <template #description v-if="description">
          {{ description }}
        </template>
        <template #buttons v-if="$slots.actions">
          <slot name="actions"></slot>
        </template>
      </PageHeading>

      <!-- Main Content -->
      <div class="mt-8">
        <slot></slot>
      </div>

      <!-- Status Messages -->
      <div
        v-if="statusMessage"
        class="mt-4 p-3 rounded-md"
        :class="{
          'bg-green-50 text-green-800 border border-green-200':
            statusType === 'success',
          'bg-amber-50 text-amber-800 border border-amber-200':
            statusType === 'info',
          'bg-orange-50 text-orange-800 border border-orange-200':
            statusType === 'warning',
          'bg-red-50 text-red-800 border border-red-200':
            statusType === 'error',
        }"
      >
        <div class="flex">
          <div class="flex-shrink-0">
            <CheckCircleIcon
              v-if="statusType === 'success'"
              class="h-5 w-5 text-green-400"
            />
            <InformationCircleIcon
              v-else-if="statusType === 'info'"
              class="h-5 w-5 text-amber-400"
            />
            <ExclamationTriangleIcon
              v-else-if="statusType === 'warning'"
              class="h-5 w-5 text-orange-400"
            />
            <XCircleIcon
              v-else-if="statusType === 'error'"
              class="h-5 w-5 text-red-400"
            />
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium">{{ statusMessage }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import {
  CheckCircleIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
} from "@heroicons/vue/24/outline";
import PageHeading from "src/components/PageHeading.vue";

// Props
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    default: "",
  },
  loading: {
    type: Boolean,
    default: false,
  },
  loadingText: {
    type: String,
    default: "Loading...",
  },
  error: {
    type: [String, Object, Error],
    default: null,
  },
  errorTitle: {
    type: String,
    default: "Error loading data",
  },
  hasData: {
    type: Boolean,
    default: true,
  },
  showEmptyState: {
    type: Boolean,
    default: false,
  },
  emptyTitle: {
    type: String,
    default: "No data available",
  },
  emptyDescription: {
    type: String,
    default: "There is no data to display.",
  },
  emptyAction: {
    type: Function,
    default: null,
  },
  emptyActionText: {
    type: String,
    default: "",
  },
  statusMessage: {
    type: String,
    default: "",
  },
  statusType: {
    type: String,
    default: "info",
    validator: (value) =>
      ["success", "info", "warning", "error"].includes(value),
  },
  onRetry: {
    type: Function,
    default: null,
  },
});

// Computed
const showEmptyStateComputed = computed(() => {
  return (
    props.showEmptyState &&
    !props.loading &&
    !props.error &&
    !props.hasData
  );
});
</script>
