<template>
  <SettingsTabPanel
    title="Change Password"
    description="Update your account password for security"
  >
    <SettingsFormCard
      title="Password Security"
      description="Enter your current password and choose a new secure password"
      :loading="loading"
      :success-message="successMessage"
      :error-message="errorMessage"
      submit-text="Update Password"
      @submit="submitForm"
    >
      <SettingsFormField
        v-model="formData.currentPassword"
        type="password"
        name="currentPassword"
        label="Current Password"
        placeholder=""
        required
        :errors="v$.currentPassword.$errors.map((e) => e.$message)"
        :col-span="6"
        autocomplete="current-password"
        @blur="v$.currentPassword.$touch"
      />

      <div class="space-y-4 sm:col-span-6">
        <SettingsFormField
          v-model="formData.newPassword"
          type="password"
          name="newPassword"
          label="New Password"
          placeholder=""
          required
          :errors="v$.newPassword.$errors.map((e) => e.$message)"
          :col-span="6"
          autocomplete="new-password"
          @blur="v$.newPassword.$touch"
          @input="checkPasswordStrength"
        />

        <!-- Password Strength Indicator -->
        <div v-if="formData.newPassword" class="mt-2">
          <div class="text-xs text-stone-600 mb-1">
            Password strength:
          </div>
          <div class="flex space-x-1">
            <div
              v-for="i in 4"
              :key="i"
              class="h-1 w-6 rounded-full"
              :class="getStrengthBarClass(i)"
            ></div>
          </div>
          <div class="text-xs mt-1" :class="getStrengthTextClass()">
            {{ passwordStrengthText }}
          </div>
        </div>
      </div>

      <SettingsFormField
        v-model="formData.confirmPassword"
        type="password"
        name="confirmPassword"
        label="Confirm New Password"
        placeholder=""
        required
        :errors="v$.confirmPassword.$errors.map((e) => e.$message)"
        :col-span="6"
        autocomplete="new-password"
        @blur="v$.confirmPassword.$touch"
      />

      <!-- Password Requirements -->
      <div
        class="p-4 bg-stone-50 border border-stone-200 rounded-md sm:col-span-6"
      >
        <h4 class="text-sm font-medium text-stone-900 mb-2">
          Password Requirements:
        </h4>
        <ul class="text-xs text-stone-600 space-y-1">
          <li class="flex items-center">
            <CheckCircleIcon
              v-if="passwordChecks.length"
              class="h-3 w-3 text-green-500 mr-2"
            />
            <XCircleIcon v-else class="h-3 w-3 text-stone-400 mr-2" />
            At least 8 characters long
          </li>
          <li class="flex items-center">
            <CheckCircleIcon
              v-if="passwordChecks.uppercase"
              class="h-3 w-3 text-green-500 mr-2"
            />
            <XCircleIcon v-else class="h-3 w-3 text-stone-400 mr-2" />
            Contains uppercase letter
          </li>
          <li class="flex items-center">
            <CheckCircleIcon
              v-if="passwordChecks.lowercase"
              class="h-3 w-3 text-green-500 mr-2"
            />
            <XCircleIcon v-else class="h-3 w-3 text-stone-400 mr-2" />
            Contains lowercase letter
          </li>
          <li class="flex items-center">
            <CheckCircleIcon
              v-if="passwordChecks.number"
              class="h-3 w-3 text-green-500 mr-2"
            />
            <XCircleIcon v-else class="h-3 w-3 text-stone-400 mr-2" />
            Contains number
          </li>
          <li class="flex items-center">
            <CheckCircleIcon
              v-if="passwordChecks.special"
              class="h-3 w-3 text-green-500 mr-2"
            />
            <XCircleIcon v-else class="h-3 w-3 text-stone-400 mr-2" />
            Contains special character
          </li>
        </ul>
      </div>
    </SettingsFormCard>
  </SettingsTabPanel>
</template>

<script setup>
import { reactive, ref, computed } from "vue";
import {
  CheckCircleIcon,
  XCircleIcon,
} from "@heroicons/vue/24/outline";
import useVuelidate from "@vuelidate/core";
import {
  required,
  minLength,
  sameAs,
  helpers,
} from "@vuelidate/validators";
import {
  useUserProfile,
  validatePasswordStrength,
} from "src/composables/use-user-profile";
import SettingsTabPanel from "src/components/SettingsTabPanel.vue";
import SettingsFormCard from "src/components/SettingsFormCard.vue";
import SettingsFormField from "src/components/SettingsFormField.vue";

// Form data
const formData = reactive({
  currentPassword: "",
  newPassword: "",
  confirmPassword: "",
});

// Reactive refs
const successMessage = ref("");
const errorMessage = ref("");
const passwordStrength = ref(0);
const passwordStrengthText = ref("");

// Password policy: must include uppercase, lowercase, number, and special character.
// Note: minimum length is enforced separately by minLength(8)
const strongPassword = (value) => {
  if (!value) return true;
  const hasUpper = /[A-Z]/.test(value);
  const hasLower = /[a-z]/.test(value);
  const hasNumber = /\d/.test(value);
  const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value);
  return hasUpper && hasLower && hasNumber && hasSpecial;
};

// Validation rules
const rules = {
  currentPassword: {
    required: helpers.withMessage(
      "Current password is required.",
      required,
    ),
  },
  newPassword: {
    required: helpers.withMessage(
      "New password is required.",
      required,
    ),
    minLength: helpers.withMessage(
      "Password must be at least 8 characters long.",
      minLength(8),
    ),
    strongPassword: helpers.withMessage(
      "Password must include uppercase, lowercase, number, and special character.",
      strongPassword,
    ),
  },
  confirmPassword: {
    required: helpers.withMessage(
      "Please confirm your new password.",
      required,
    ),
    sameAsPassword: helpers.withMessage(
      "Passwords do not match.",
      sameAs(computed(() => formData.newPassword)),
    ),
  },
};

const v$ = useVuelidate(rules, formData);

// Use user profile composable
const {
  updatePassword,
  passwordLoading: loading,
  passwordError,
  clearMessages,
} = useUserProfile();

// Password strength checks using composable
const passwordChecks = computed(() => {
  return validatePasswordStrength(formData.newPassword).checks;
});

// Check password strength
const checkPasswordStrength = () => {
  const result = validatePasswordStrength(formData.newPassword);
  passwordStrength.value = result.score;
  passwordStrengthText.value = result.strength;
};

// Strength bar classes
const getStrengthBarClass = (index) => {
  if (passwordStrength.value >= index) {
    if (passwordStrength.value <= 1) return "bg-red-500";
    if (passwordStrength.value === 2) return "bg-orange-500";
    if (passwordStrength.value === 3) return "bg-yellow-500";
    if (passwordStrength.value === 4) return "bg-blue-500";
    return "bg-green-500";
  }
  return "bg-gray-200";
};

// Strength text classes
const getStrengthTextClass = () => {
  if (passwordStrength.value <= 1) return "text-red-600";
  if (passwordStrength.value === 2) return "text-orange-600";
  if (passwordStrength.value === 3) return "text-yellow-600";
  if (passwordStrength.value === 4) return "text-blue-600";
  return "text-green-600";
};

// Remove old GraphQL mutation - now using composable

// Form submission
const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;

  clearMessages();
  errorMessage.value = "";
  successMessage.value = "";

  try {
    const message = await updatePassword({
      currentPassword: formData.currentPassword,
      newPassword: formData.newPassword,
      confirmPassword: formData.confirmPassword,
    });

    successMessage.value = message;

    // Clear form
    formData.currentPassword = "";
    formData.newPassword = "";
    formData.confirmPassword = "";
    v$.value.$reset();

    // Clear success message after 5 seconds
    setTimeout(() => {
      successMessage.value = "";
    }, 5000);
  } catch (error) {
    console.error("Password change error:", error);
    errorMessage.value =
      passwordError.value ||
      "An error occurred while changing your password. Please try again.";
  }
};
</script>
