# User Profile Management Feature

## Overview

The User Profile Management feature provides comprehensive functionality for users to view, edit, and manage their profile information within the settings section of the application. This feature includes profile viewing, editing, and password management capabilities.

## Features

### 1. Profile Information Display

- **Current User Data**: Display username/email, full name, profile picture, account creation date, and organization information
- **Profile Picture**: Support for displaying user avatars with fallback to user initials
- **Account Metadata**: Show account creation and last updated timestamps
- **Responsive Design**: Optimized for both desktop and mobile viewing

### 2. Profile Editing

- **Editable Fields**: First name, last name, email address, and profile picture
- **File Upload**: Profile picture upload with validation (image types, file size limits)
- **Form Validation**: Comprehensive client-side validation using Vuelidate
- **Real-time Preview**: Live preview of profile picture changes
- **Error Handling**: Detailed error messages for validation failures and server errors

### 3. Password Management

- **Secure Password Change**: Current password verification required
- **Password Strength Validation**: Real-time password strength indicator
- **Security Requirements**: Enforced password complexity rules
- **Visual Feedback**: Color-coded strength indicator and requirement checklist
- **Confirmation Matching**: Ensures new password and confirmation match

## Technical Implementation

### Components

#### ProfileTabPanel.vue

- **Purpose**: Displays current user profile information
- **Features**:
  - Fetches user data using `useUserProfile` composable
  - Handles loading and error states
  - Emits edit events to switch to edit mode
  - Responsive layout with proper accessibility

#### ProfileEditForm.vue

- **Purpose**: Provides form interface for editing profile information
- **Features**:
  - Form validation with Vuelidate
  - File upload handling for profile pictures
  - Success/error message display
  - Loading states during submission
  - Emits success/cancel events

#### PasswordChangeForm.vue

- **Purpose**: Secure password change functionality
- **Features**:
  - Current password verification
  - Password strength validation and visualization
  - Security requirements checklist
  - Form validation and error handling
  - Success feedback and form clearing

### Composables

#### useUserProfile.js

- **Purpose**: Centralized user profile state management
- **Features**:
  - GraphQL query/mutation handling
  - Reactive user data
  - Profile update methods
  - Password change methods
  - Error and success state management
  - Cache-first data fetching for offline support

### GraphQL Schema Extensions

#### User Type

```graphql
type User {
  id: ID!
  email: String!
  firstName: String
  lastName: String
  fullName: String
  profilePicture: String
  createdAt: String!
  updatedAt: String!
  organisationId: Int
}
```

#### Input Types

```graphql
input UserProfileInput {
  firstName: String
  lastName: String
  email: String
  profilePicture: String
}

input ChangePasswordInput {
  currentPassword: String!
  newPassword: String!
  confirmPassword: String!
}
```

#### Queries and Mutations

```graphql
type Query {
  currentUser: User
  user(id: ID!): User
}

type Mutation {
  updateUserProfile(input: UserProfileInput!): UserProfileResponse!
  changePassword(input: ChangePasswordInput!): ChangePasswordResponse!
}
```

## Security Features

### Password Security

- **Minimum Length**: 8 characters required
- **Complexity Requirements**: Uppercase, lowercase, numbers, and special characters
- **Strength Scoring**: 5-level strength indicator (Very Weak to Strong)
- **Current Password Verification**: Required for password changes
- **Secure Input Handling**: Password fields use proper input types

### Data Validation

- **Client-side Validation**: Immediate feedback using Vuelidate
- **Server-side Validation**: Backend validation for all mutations
- **File Upload Security**: Type and size validation for profile pictures
- **Email Validation**: Proper email format validation

### Authentication Integration

- **Token-based Authentication**: Integrates with existing cookie-based auth
- **User Context**: Automatic user data fetching based on authentication state
- **Offline Support**: Cache-first strategy for user data

## Usage

### Accessing Profile Management

1. Navigate to Settings page
2. Select "Profile" tab to view/edit profile information
3. Select "Password" tab to change password

### Editing Profile

1. Click "Edit Profile" button in Profile tab
2. Modify desired fields
3. Upload new profile picture if needed
4. Click "Save Changes" to submit
5. View success confirmation or error messages

### Changing Password

1. Navigate to Password tab
2. Enter current password
3. Enter new password (watch strength indicator)
4. Confirm new password
5. Click "Change Password" to submit
6. View success confirmation

## Testing

### Unit Tests

- **Composable Tests**: `use-user-profile.spec.js`
- **Component Tests**:
  - `ProfileTabPanel.spec.js`
  - `ProfileEditForm.spec.js`
  - `PasswordChangeForm.spec.js`

### Test Coverage

- Form validation scenarios
- User interaction flows
- Error handling
- Loading states
- Success/failure feedback
- Accessibility features

## Accessibility Features

### ARIA Support

- Proper ARIA labels for form controls
- Error message announcements
- Loading state indicators
- Screen reader friendly structure

### Keyboard Navigation

- Full keyboard accessibility
- Logical tab order
- Focus management
- Enter key form submission

### Visual Design

- High contrast error/success messages
- Clear visual hierarchy
- Responsive design
- Color-blind friendly indicators

## Browser Compatibility

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile Support**: iOS Safari 14+, Chrome Mobile 90+
- **Progressive Enhancement**: Graceful degradation for older browsers

## Performance Considerations

### Optimization Features

- **Lazy Loading**: Components loaded on demand
- **Cache-first Strategy**: Offline-capable data fetching
- **Debounced Validation**: Efficient form validation
- **Image Optimization**: Client-side image resizing for uploads

### Bundle Size

- **Minimal Dependencies**: Uses existing Quasar and Vue ecosystem
- **Tree Shaking**: Only imports used utilities
- **Code Splitting**: Components can be lazy-loaded

## Future Enhancements

### Potential Improvements

- **Two-factor Authentication**: Add 2FA setup in profile
- **Account Deletion**: Self-service account deletion
- **Data Export**: Profile data export functionality
- **Social Login Integration**: Connect social media accounts
- **Profile Themes**: Customizable profile appearance
- **Activity Log**: View account activity history

### Technical Debt

- **Error Boundary**: Add error boundaries for better error handling
- **Internationalization**: Add i18n support for multiple languages
- **Advanced Validation**: Server-side validation feedback integration
- **File Storage**: Implement proper file storage service integration

## Required Server-Side Changes (Rails Backend)

### 1. GraphQL Schema Updates

The Rails backend needs to implement the following GraphQL schema additions:

#### User Type Definition

```ruby
# app/graphql/types/user_type.rb
module Types
  class UserType < Types::BaseObject
    field :id, ID, null: false
    field :email, String, null: false
    field :first_name, String, null: true
    field :last_name, String, null: true
    field :full_name, String, null: true
    field :profile_picture, String, null: true
    field :created_at, String, null: false
    field :updated_at, String, null: false
    field :organisation_id, Integer, null: true

    def full_name
      "#{object.first_name} #{object.last_name}".strip
    end
  end
end
```

#### Input Types

```ruby
# app/graphql/types/user_profile_input_type.rb
module Types
  class UserProfileInputType < Types::BaseInputObject
    argument :first_name, String, required: false
    argument :last_name, String, required: false
    argument :email, String, required: false
    argument :profile_picture, String, required: false
  end
end

# app/graphql/types/change_password_input_type.rb
module Types
  class ChangePasswordInputType < Types::BaseInputObject
    argument :current_password, String, required: true
    argument :new_password, String, required: true
    argument :confirm_password, String, required: true
  end
end
```

#### Response Types

```ruby
# app/graphql/types/user_profile_response_type.rb
module Types
  class UserProfileResponseType < Types::BaseObject
    field :success, Boolean, null: false
    field :user, Types::UserType, null: true
    field :errors, [Types::ErrorType], null: false
  end
end

# app/graphql/types/change_password_response_type.rb
module Types
  class ChangePasswordResponseType < Types::BaseObject
    field :success, Boolean, null: false
    field :message, String, null: true
    field :errors, [Types::ErrorType], null: false
  end
end

# app/graphql/types/error_type.rb
module Types
  class ErrorType < Types::BaseObject
    field :path, [String], null: false
    field :message, String, null: false
  end
end
```

### 2. GraphQL Queries and Mutations

#### Query Resolver

```ruby
# app/graphql/types/query_type.rb
module Types
  class QueryType < Types::BaseObject
    field :current_user, Types::UserType, null: true
    field :user, Types::UserType, null: true do
      argument :id, ID, required: true
    end

    def current_user
      context[:current_user]
    end

    def user(id:)
      User.find(id)
    end
  end
end
```

#### Mutation Resolvers

```ruby
# app/graphql/mutations/update_user_profile.rb
module Mutations
  class UpdateUserProfile < BaseMutation
    argument :input, Types::UserProfileInputType, required: true
    type Types::UserProfileResponseType

    def resolve(input:)
      user = context[:current_user]

      return {
        success: false,
        user: nil,
        errors: [{ path: ['authentication'], message: 'User not authenticated' }]
      } unless user

      if user.update(input.to_h.compact)
        {
          success: true,
          user: user,
          errors: []
        }
      else
        {
          success: false,
          user: nil,
          errors: user.errors.full_messages.map { |msg|
            { path: ['user'], message: msg }
          }
        }
      end
    end
  end
end

# app/graphql/mutations/change_password.rb
module Mutations
  class ChangePassword < BaseMutation
    argument :input, Types::ChangePasswordInputType, required: true
    type Types::ChangePasswordResponseType

    def resolve(input:)
      user = context[:current_user]

      return {
        success: false,
        message: nil,
        errors: [{ path: ['authentication'], message: 'User not authenticated' }]
      } unless user

      # Verify current password
      unless user.authenticate(input[:current_password])
        return {
          success: false,
          message: nil,
          errors: [{ path: ['current_password'], message: 'Current password is incorrect' }]
        }
      end

      # Validate password confirmation
      unless input[:new_password] == input[:confirm_password]
        return {
          success: false,
          message: nil,
          errors: [{ path: ['confirm_password'], message: 'New passwords do not match' }]
        }
      end

      # Update password
      if user.update(password: input[:new_password])
        {
          success: true,
          message: 'Password changed successfully!',
          errors: []
        }
      else
        {
          success: false,
          message: nil,
          errors: user.errors.full_messages.map { |msg|
            { path: ['new_password'], message: msg }
          }
        }
      end
    end
  end
end
```

### 3. Database Migrations

If the User model doesn't have the required fields:

```ruby
# db/migrate/add_profile_fields_to_users.rb
class AddProfileFieldsToUsers < ActiveRecord::Migration[7.0]
  def change
    add_column :users, :first_name, :string
    add_column :users, :last_name, :string
    add_column :users, :profile_picture, :string
    add_column :users, :organisation_id, :integer

    add_index :users, :organisation_id
  end
end
```

### 4. Model Validations

```ruby
# app/models/user.rb
class User < ApplicationRecord
  has_secure_password

  validates :email, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :first_name, length: { maximum: 50 }
  validates :last_name, length: { maximum: 50 }
  validates :password, length: { minimum: 8 }, if: :password_required?

  # Custom password strength validation
  validate :password_complexity, if: :password_required?

  private

  def password_complexity
    return unless password.present?

    errors.add(:password, 'must include at least one uppercase letter') unless password.match(/[A-Z]/)
    errors.add(:password, 'must include at least one lowercase letter') unless password.match(/[a-z]/)
    errors.add(:password, 'must include at least one number') unless password.match(/\d/)
    errors.add(:password, 'must include at least one special character') unless password.match(/[!@#$%^&*(),.?":{}|<>]/)
  end

  def password_required?
    password.present? || password_confirmation.present?
  end
end
```

### 5. File Upload Handling (Optional)

For profile picture uploads, you may want to integrate with a file storage service:

```ruby
# app/models/user.rb (addition)
class User < ApplicationRecord
  # ... existing code ...

  # If using Active Storage
  has_one_attached :profile_picture_file

  def profile_picture_url
    profile_picture_file.attached? ? Rails.application.routes.url_helpers.rails_blob_url(profile_picture_file) : profile_picture
  end
end
```

### 6. Authentication Context

Ensure your GraphQL context includes the current user:

```ruby
# app/controllers/graphql_controller.rb
class GraphqlController < ApplicationController
  def execute
    context = {
      current_user: current_user,
      # ... other context
    }

    result = OccumedSchema.execute(
      params[:query],
      variables: params[:variables],
      context: context,
      operation_name: params[:operationName]
    )

    render json: result
  end

  private

  def current_user
    # Your authentication logic here
    # e.g., User.find_by(id: session[:user_id])
  end
end
```

These server-side changes will provide the necessary backend support for the frontend user profile management features.
