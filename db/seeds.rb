# frozen_string_literal: true

require "faker"

Rails.logger.info "Starting seed data generation for stress testing..."

# Create primary organisation and user
Rails.logger.info "Creating primary organisation and user..."
org = Organisation.find_or_create_by(organisation_name: "Marian<PERSON>ed") do |o|
  o.email_address = "<EMAIL>"
  o.contact_number = Faker::PhoneNumber.phone_number
  o.registration_number = Faker::Company.duns_number
  o.web_address = "https://marianmed.com"
  o.initials = "MM"
end

# Create additional organisations for diversity
Rails.logger.info "Creating additional organisations..."
additional_orgs = []
9.times do
  additional_orgs << Organisation.create!(
    organisation_name: Faker::Company.name + " Medical",
    email_address: Faker::Internet.email,
    contact_number: Faker::PhoneNumber.phone_number,
    registration_number: Faker::Company.duns_number,
    web_address: Faker::Internet.url,
    initials: Faker::Lorem.characters(number: 2).upcase
  )
end

all_orgs = [org] + additional_orgs

# Create admin user
usr = User.find_or_create_by(email: "<EMAIL>") do |u|
  u.first_name = "vin"
  u.last_name = "ken"
  u.password = "123456"
  u.password_confirmation = "123456"
  u.confirmed_at = DateTime.now
end

OrganisationUser.find_or_create_by(user_id: usr.id, organisation_id: org.id)

# Create additional users
Rails.logger.info "Creating additional users..."
users = [usr]
40.times do
  user = User.create!(
    first_name: Faker::Name.first_name,
    last_name: Faker::Name.last_name,
    email: Faker::Internet.unique.email,
    password: "password123",
    password_confirmation: "password123",
    confirmed_at: DateTime.now
  )
  
  # Assign to random organisation
  selected_org = all_orgs.sample
  OrganisationUser.create!(user_id: user.id, organisation_id: selected_org.id)
  users << user
end

# Create clinics for each organisation
Rails.logger.info "Creating clinics..."
clinics = []
all_orgs.each do |organisation|
  rand(2..5).times do
    clinic = Clinic.create!(
      clinic_name: "#{Faker::Address.city} Medical Center",
      physical_address: Faker::Address.street_address,
      physical_address_2: Faker::Address.secondary_address,
      phone_number: Faker::PhoneNumber.phone_number,
      details: Faker::Lorem.sentence,
      organisation_id: organisation.id
    )
    clinics << clinic
  end
end

# Create teams
Rails.logger.info "Creating teams..."
teams = []
all_orgs.each do |organisation|
  rand(1..3).times do
    team = Team.create!(
      team_name: "#{Faker::Lorem.word.capitalize} Team",
      organisation_id: organisation.id
    )
    teams << team
    
    # Associate teams with clinics
    organisation.clinics.sample(rand(1..2)).each do |clinic|
      ClinicTeam.create!(clinic: clinic, team: team)
    end
  end
end

# Create companies with proper distribution
Rails.logger.info "Creating 150 companies..."
companies = []
150.times do |i|
  org_to_use = all_orgs.sample
  company = Company.create!(
    name: Faker::Company.unique.name,
    email: Faker::Internet.email,
    industry_sector: Faker::Company.industry,
    about: Faker::Company.catch_phrase,
    organisation_id: org_to_use.id,
    street_address: Faker::Address.street_address,
    city: Faker::Address.city,
    suburb: Faker::Address.community,
    phone_number: Faker::PhoneNumber.phone_number
  )
  companies << company
  
  Rails.logger.info "Created company #{i + 1}/150" if (i + 1) % 20 == 0
end

# Create 6000 patients with employments
Rails.logger.info "Creating 6000 patients with employments..."
patients = []
employments = []

6000.times do |i|
  org_to_use = all_orgs.sample
  
  patient = Patient.create!(
    first_name: Faker::Name.first_name,
    last_name: Faker::Name.last_name,
    dob: Faker::Date.birthday(min_age: 18, max_age: 65),
    gender: %w[Male Female].sample,
    identification_number: Faker::Number.unique.number(digits: 13).to_s,
    email: Faker::Internet.unique.email,
    phone_number: Faker::PhoneNumber.cell_phone,
    organisation_id: org_to_use.id
  )
  patients << patient
  
  # Create 1-3 employments per patient
  num_employments = rand(1..3)
  num_employments.times do
    company = companies.sample
    
    # Ensure employment is with company in same organisation
    if company.organisation_id == patient.organisation_id
      induction_date = Faker::Date.between(from: 3.years.ago, to: 1.year.ago)
      termination_date = rand < 0.7 ? nil : Faker::Date.between(from: induction_date, to: Date.current)
      
      employment = Employment.create!(
        patient: patient,
        company: company,
        position: Faker::Job.title,
        department: Faker::Commerce.department,
        employment_type: %w[Full-time Part-time Contract Temporary].sample,
        induction_date: induction_date,
        termination_date: termination_date,
        termination_reason: termination_date ? %w[Resignation Termination Contract_End Retirement].sample : nil
      )
      employments << employment
    end
  end
  
  Rails.logger.info "Created patient #{i + 1}/6000" if (i + 1) % 1000 == 0
end

# Create assessment-related data first
Rails.logger.info "Creating lab tests..."
lab_tests = []
2000.times do
  patient = patients.sample
  clinic = clinics.select { |c| c.organisation_id == patient.organisation_id }.sample
  next unless clinic
  
  lab_test = LabTest.create!(
    patient: patient,
    clinic: clinic,
    name: "Lab Test #{Faker::Alphanumeric.alphanumeric(number: 8).upcase}",
    cannabis: %w[Positive Negative Not\ Performed].sample,
    performed_by: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
    date_performed: Faker::Date.between(from: 2.years.ago, to: Date.current)
  )
  lab_tests << lab_test
end

Rails.logger.info "Creating audio tests..."
audio_tests = []
1600.times do
  patient = patients.sample
  clinic = clinics.select { |c| c.organisation_id == patient.organisation_id }.sample
  next unless clinic
  
  audio_test = Audio.create!(
    patient: patient,
    clinic: clinic,
    name: "Audio Test #{Faker::Alphanumeric.alphanumeric(number: 8).upcase}",
    result: %w[Normal Mild_Loss Moderate_Loss Severe_Loss].sample,
    performed_by: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
    date_performed: Faker::Date.between(from: 2.years.ago, to: Date.current)
  )
  audio_tests << audio_test
end

# Create 10000 evaluations
Rails.logger.info "Creating 10000 evaluations..."
medical_types = %w[Pre-employment Annual Periodic Return-to-work Exit Fitness-for-duty]
outcomes = %w[Fit Fit-with-restrictions Unfit Pending Review]

10000.times do |i|
  patient = patients.sample
  
  # Get a random employment for this patient
  patient_employments = employments.select { |e| e.patient_id == patient.id }
  next if patient_employments.empty?
  
  employment = patient_employments.sample
  clinic = clinics.select { |c| c.organisation_id == patient.organisation_id }.sample
  next unless clinic
  
  exam_date = Faker::Date.between(from: 2.years.ago, to: Date.current)
  expiry_date = exam_date + rand(6..24).months
  
  # Create evaluation with unique name
  evaluation_name = "#{Faker::Lorem.characters(number: 3, min_numeric: 0).upcase}#{Faker::Number.unique.number(digits: 7)}"
  
  # Randomly assign assessment types
  audio_performed = rand < 0.6
  lab_test_performed = rand < 0.7
  
  evaluation = Evaluation.create!(
    name: evaluation_name,
    patient: patient,
    employment: employment,
    clinic: clinic,
    medical_type: medical_types.sample,
    outcome: outcomes.sample,
    outcome_comment: Faker::Lorem.sentence,
    medical_examination_date: exam_date,
    medical_expiry_date: expiry_date,
    status: %w[Draft In-progress Completed Signed].sample,
    
    # Assessment flags
    audio_performed: audio_performed,
    cannabis_performed: rand < 0.4,
    ecg_performed: rand < 0.3,
    heat_performed: rand < 0.2,
    height_performed: rand < 0.25,
    visual_performed: rand < 0.8,
    xray_performed: rand < 0.5,
    spiro_performed: rand < 0.4,
    physical_exam_performed: rand < 0.9,
    
    # Optional associations
    lab_test: lab_test_performed ? lab_tests.sample : nil,
    audio: audio_performed ? audio_tests.sample : nil,
    
    exclusion: rand < 0.1,
    exclusion_comment: rand < 0.1 ? Faker::Lorem.sentence : nil,
    referral: rand < 0.15,
    referral_comment: rand < 0.15 ? Faker::Lorem.sentence : nil
  )
  
  Rails.logger.info "Created evaluation #{i + 1}/10000" if (i + 1) % 2000 == 0
end

# Create additional assessment data
Rails.logger.info "Creating additional assessment data..."

# Create more spiro tests
1000.times do
  patient = patients.sample
  clinic = clinics.select { |c| c.organisation_id == patient.organisation_id }.sample
  next unless clinic
  
  Spiro.create!(
    patient: patient,
    clinic: clinic,
    name: "Spiro Test #{Faker::Alphanumeric.alphanumeric(number: 8).upcase}",
    result: %w[Normal Restricted Obstructive Mixed].sample,
    performed_by: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
    date_performed: Faker::Date.between(from: 2.years.ago, to: Date.current)
  )
end

# Create physical exams
2400.times do
  patient = patients.sample
  clinic = clinics.select { |c| c.organisation_id == patient.organisation_id }.sample
  next unless clinic
  
  Physical.create!(
    patient: patient,
    clinic: clinic,
    result: %w[Normal Abnormal Requires-follow-up].sample,
    performed_by: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
    date_performed: Faker::Date.between(from: 2.years.ago, to: Date.current),
    height: "#{rand(150..200)}cm",
    weight: "#{rand(50..120)}kg",
    pulse: "#{rand(60..100)}bpm",
    blood_pressure: "#{rand(110..140)}/#{rand(70..90)}"
  )
end

# Create vision screenings
2000.times do
  patient = patients.sample
  clinic = clinics.select { |c| c.organisation_id == patient.organisation_id }.sample
  next unless clinic
  
  VisionScreening.create!(
    patient: patient,
    clinic: clinic,
    name: "Vision Test #{Faker::Alphanumeric.alphanumeric(number: 8).upcase}",
    performed_by: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
    date_of_screening: Faker::Date.between(from: 2.years.ago, to: Date.current),
    snellen_left_eye: %w[6/6 6/9 6/12 6/15 6/18 6/24].sample,
    snellen_right_eye: %w[6/6 6/9 6/12 6/15 6/18 6/24].sample,
    color_discrimination: %w[Normal Deficient].sample
  )
end

# Create ECG tests (check if model exists)
begin
  800.times do
    patient = patients.sample
    clinic = clinics.select { |c| c.organisation_id == patient.organisation_id }.sample
    next unless clinic
    
    if defined?(Ecg)
      Ecg.create!(
        patient: patient,
        clinic: clinic,
        result: %w[Normal Abnormal Borderline].sample,
        performed_by: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
        date_performed: Faker::Date.between(from: 2.years.ago, to: Date.current)
      )
    end
  end
rescue NameError
  Rails.logger.info "ECG model not available, skipping..."
end

# Create X-ray tests (check if model exists)
begin
  1200.times do
    patient = patients.sample
    clinic = clinics.select { |c| c.organisation_id == patient.organisation_id }.sample
    next unless clinic
    
    if defined?(Xray)
      Xray.create!(
        patient: patient,
        clinic: clinic,
        result: %w[Normal Abnormal Pneumoconiosis Suspicious].sample,
        performed_by: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
        date_performed: Faker::Date.between(from: 2.years.ago, to: Date.current)
      )
    end
  end
rescue NameError
  Rails.logger.info "Xray model not available, skipping..."
end

# Create heat stress assessments (check if model exists)
begin
  600.times do
    patient = patients.sample
    clinic = clinics.select { |c| c.organisation_id == patient.organisation_id }.sample
    next unless clinic
    
    if defined?(Heat)
      Heat.create!(
        patient: patient,
        clinic: clinic,
        result: %w[Fit Fit-with-precautions Unfit].sample,
        performed_by: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
        date_performed: Faker::Date.between(from: 2.years.ago, to: Date.current)
      )
    end
  end
rescue NameError
  Rails.logger.info "Heat model not available, skipping..."
end

# Create height assessments (check if model exists)
begin
  500.times do
    patient = patients.sample
    clinic = clinics.select { |c| c.organisation_id == patient.organisation_id }.sample
    next unless clinic
    
    if defined?(Height)
      Height.create!(
        patient: patient,
        clinic: clinic,
        result: %w[Fit Fit-with-restrictions Unfit].sample,
        performed_by: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
        date_performed: Faker::Date.between(from: 2.years.ago, to: Date.current)
      )
    end
  end
rescue NameError
  Rails.logger.info "Height model not available, skipping..."
end

# Create patient notes
Rails.logger.info "Creating patient notes..."
4000.times do
  patient = patients.sample
  PatientNote.create!(
    note: Faker::Lorem.paragraph(sentence_count: rand(2..6)),
    last_edited_by: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
    patient: patient
  )
end

# Create exclusions and referrals
Rails.logger.info "Creating exclusions and referrals..."

1000.times do
  patient = patients.sample
  Exclusion.create!(
    patient: patient,
    note: Faker::Lorem.sentence,
    category: %w[Medical Legal Temporary Permanent].sample
  )
end

600.times do
  patient = patients.sample
  specialist = %w[Cardiologist Pulmonologist Orthopedic ENT Occupational-Medicine].sample
  Referral.create!(
    patient: patient,
    specialist_type: specialist,
    note: Faker::Lorem.sentence,
    issues: Faker::Lorem.paragraph(sentence_count: 2),
    attention: "Dr. #{Faker::Name.first_name} #{Faker::Name.last_name}",
    medical_centre: "#{Faker::Address.city} #{specialist} Center",
    referral_date: Faker::Date.between(from: 1.year.ago, to: Date.current)
  )
end

Rails.logger.info "Seed data generation completed successfully!"
Rails.logger.info "Summary:"
Rails.logger.info "- Organisations: #{Organisation.count}"
Rails.logger.info "- Users: #{User.count}"
Rails.logger.info "- Clinics: #{Clinic.count}"
Rails.logger.info "- Teams: #{Team.count}"
Rails.logger.info "- Companies: #{Company.count}"
Rails.logger.info "- Patients: #{Patient.count}"
Rails.logger.info "- Employments: #{Employment.count}"
Rails.logger.info "- Evaluations: #{Evaluation.count}"
Rails.logger.info "- Lab Tests: #{LabTest.count}"
Rails.logger.info "- Audio Tests: #{Audio.count}"
Rails.logger.info "- Vision Screenings: #{VisionScreening.count}"
Rails.logger.info "- Physical Exams: #{Physical.count}"
Rails.logger.info "- Patient Notes: #{PatientNote.count}"
