import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { mount } from '@vue/test-utils'
import { ref } from 'vue'
import ProfileEditForm from 'src/components/ProfileEditForm.vue'

// Mock the user profile composable
const mockUpdateProfile = jest.fn()
const mockClearMessages = jest.fn()

const mockUserProfile = {
  updateProfile: mockUpdateProfile,
  updateLoading: ref(false),
  updateSuccess: ref(false),
  updateError: ref(''),
  clearMessages: mockClearMessages
}

jest.mock('src/composables/use-user-profile', () => ({
  useUserProfile: () => mockUserProfile
}))

// Mock PageHeading component
jest.mock('src/components/PageHeading.vue', () => ({
  name: 'PageHeading',
  template: '<div class="page-heading"><slot name="heading"></slot><slot name="description"></slot><slot name="buttons"></slot></div>'
}))

// Mock Vuelidate
jest.mock('@vuelidate/core', () => ({
  default: jest.fn(() => ({
    $validate: jest.fn(() => Promise.resolve(true)),
    $reset: jest.fn(),
    firstName: { $error: false, $errors: [] },
    lastName: { $error: false, $errors: [] },
    email: { $error: false, $errors: [] }
  }))
}))

describe('ProfileEditForm', () => {
  let wrapper
  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    fullName: 'John Doe',
    profilePicture: 'https://example.com/avatar.jpg'
  }

  beforeEach(() => {
    jest.clearAllMocks()

    wrapper = mount(ProfileEditForm, {
      props: {
        user: mockUser
      },
      global: {
        stubs: {
          'q-btn': {
            name: 'QBtn',
            template: '<button type="button" :disabled="disabled"><slot></slot></button>',
            props: ['flat', 'class', 'padding', 'no-caps', 'disabled', 'loading']
          },
          'q-input': {
            name: 'QInput',
            template: '<input :value="modelValue" @input="$emit(\'update:modelValue\', $event.target.value)" />',
            props: ['modelValue', 'label', 'type', 'outlined', 'dense', 'error', 'error-message'],
            emits: ['update:modelValue']
          },
          'q-file': {
            name: 'QFile',
            template: '<input type="file" @change="$emit(\'update:modelValue\', $event.target.files[0])" />',
            props: ['modelValue', 'label', 'outlined', 'dense', 'accept', 'error', 'error-message'],
            emits: ['update:modelValue']
          }
        }
      }
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('form initialization', () => {
    it('should populate form with user data', () => {
      const inputs = wrapper.findAll('input')
      const emailInput = inputs.find(input => input.element.value === '<EMAIL>')
      const firstNameInput = inputs.find(input => input.element.value === 'John')
      const lastNameInput = inputs.find(input => input.element.value === 'Doe')

      expect(emailInput).toBeTruthy()
      expect(firstNameInput).toBeTruthy()
      expect(lastNameInput).toBeTruthy()
    })

    it('should show current profile picture', () => {
      const img = wrapper.find('img')
      expect(img.exists()).toBe(true)
      expect(img.attributes('src')).toBe('https://example.com/avatar.jpg')
    })

    it('should show user icon when no profile picture', async () => {
      await wrapper.setProps({
        user: { ...mockUser, profilePicture: null }
      })

      const userIcon = wrapper.find('[data-testid="user-icon"]')
      expect(userIcon.exists()).toBe(true)
    })
  })

  describe('form validation', () => {
    it('should validate required fields', async () => {
      const firstNameInput = wrapper.findAll('input')[0]
      await firstNameInput.setValue('')

      const form = wrapper.find('form')
      await form.trigger('submit')

      // Validation should prevent submission
      expect(mockUpdateProfile).not.toHaveBeenCalled()
    })

    it('should validate email format', async () => {
      const emailInput = wrapper.findAll('input').find(input =>
        input.element.value === '<EMAIL>'
      )
      await emailInput.setValue('invalid-email')

      const form = wrapper.find('form')
      await form.trigger('submit')

      expect(mockUpdateProfile).not.toHaveBeenCalled()
    })
  })

  describe('file upload', () => {
    it('should handle profile picture upload', async () => {
      const fileInput = wrapper.find('input[type="file"]')

      // Create a mock file
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' })

      // Mock FileReader
      const mockFileReader = {
        readAsDataURL: jest.fn(),
        result: 'data:image/jpeg;base64,test'
      }
      global.FileReader = jest.fn(() => mockFileReader)

      await fileInput.setValue([file])

      // Simulate FileReader onload
      mockFileReader.onload({ target: { result: 'data:image/jpeg;base64,test' } })

      expect(mockFileReader.readAsDataURL).toHaveBeenCalledWith(file)
    })

    it('should validate file size', async () => {
      const fileInput = wrapper.find('input[type="file"]')

      // Create a large mock file (6MB)
      const largeFile = new File(['x'.repeat(6 * 1024 * 1024)], 'large.jpg', {
        type: 'image/jpeg'
      })

      await fileInput.setValue([largeFile])

      expect(wrapper.text()).toContain('File size must be less than 5MB')
    })

    it('should validate file type', async () => {
      const fileInput = wrapper.find('input[type="file"]')

      // Create a non-image file
      const textFile = new File(['test'], 'test.txt', { type: 'text/plain' })

      await fileInput.setValue([textFile])

      expect(wrapper.text()).toContain('Please select a valid image file')
    })
  })

  describe('form submission', () => {
    it('should submit form with valid data', async () => {
      mockUpdateProfile.mockResolvedValue({ id: '1', firstName: 'Jane' })

      const firstNameInput = wrapper.findAll('input')[0]
      await firstNameInput.setValue('Jane')

      const form = wrapper.find('form')
      await form.trigger('submit')

      expect(mockClearMessages).toHaveBeenCalled()
      expect(mockUpdateProfile).toHaveBeenCalledWith({
        firstName: 'Jane',
        lastName: 'Doe',
        email: '<EMAIL>',
        profilePicture: null
      })
    })

    it('should show loading state during submission', async () => {
      mockUserProfile.updateLoading.value = true
      await wrapper.vm.$nextTick()

      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('disabled')).toBeDefined()
    })

    it('should emit success event on successful update', async () => {
      const updatedUser = { id: '1', firstName: 'Jane' }
      mockUpdateProfile.mockResolvedValue(updatedUser)

      const form = wrapper.find('form')
      await form.trigger('submit')

      // Wait for async operations
      await wrapper.vm.$nextTick()

      expect(wrapper.emitted('success')).toBeTruthy()
      expect(wrapper.emitted('success')[0]).toEqual([updatedUser])
    })

    it('should handle submission errors', async () => {
      const errorMessage = 'Email already exists'
      mockUpdateProfile.mockRejectedValue(new Error(errorMessage))
      mockUserProfile.updateError.value = errorMessage

      const form = wrapper.find('form')
      await form.trigger('submit')

      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain(errorMessage)
    })
  })

  describe('user interactions', () => {
    it('should emit cancel event when cancel button is clicked', async () => {
      const cancelButton = wrapper.findAll('button').find(btn =>
        btn.text().includes('Cancel')
      )

      await cancelButton.trigger('click')

      expect(wrapper.emitted('cancel')).toBeTruthy()
    })

    it('should clear error messages when form is modified', async () => {
      // This test would need to be implemented based on actual form behavior
      // Currently the form doesn't automatically clear errors on input change
      expect(true).toBe(true)
    })
  })

  describe('accessibility', () => {
    it('should have proper form labels', () => {
      const labels = wrapper.findAll('label')
      expect(labels.length).toBeGreaterThan(0)
    })

    it('should associate labels with inputs', () => {
      const inputs = wrapper.findAll('input')
      inputs.forEach(input => {
        expect(input.attributes('id')).toBeTruthy()
      })
    })

    it('should show error messages with proper ARIA attributes', async () => {
      wrapper.vm.errorMessage = 'Test error'
      await wrapper.vm.$nextTick()

      const errorElement = wrapper.find('[role="alert"]')
      expect(errorElement.exists()).toBe(true)
      expect(errorElement.text()).toContain('Test error')
    })
  })
})
