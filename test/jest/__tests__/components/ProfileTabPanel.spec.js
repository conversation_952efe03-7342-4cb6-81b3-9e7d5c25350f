import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { mount } from '@vue/test-utils'
import { ref } from 'vue'
import ProfileTabPanel from 'src/components/ProfileTabPanel.vue'

// Mock the user profile composable
const mockUserProfile = {
  currentUser: ref({
    id: '1',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON><PERSON>',
    fullName: '<PERSON>',
    profilePicture: 'https://example.com/avatar.jpg',
    createdAt: '2022-01-15T10:30:00Z',
    updatedAt: '2023-01-15T10:30:00Z',
    organisationId: 1
  }),
  userLoading: ref(false),
  userError: ref(null),
  refetchUser: jest.fn()
}

jest.mock('src/composables/use-user-profile', () => ({
  useUserProfile: () => mockUserProfile,
  formatUserDate: (date) => {
    if (!date) return 'Not available'
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}))

// Mock PageHeading component
jest.mock('src/components/PageHeading.vue', () => ({
  name: 'PageHeading',
  template: '<div class="page-heading"><slot name="heading"></slot><slot name="description"></slot><slot name="buttons"></slot></div>'
}))

// Mock Quasar components
jest.mock('quasar', () => ({
  useQuasar: () => ({})
}))

describe('ProfileTabPanel', () => {
  let wrapper

  beforeEach(() => {
    jest.clearAllMocks()
    
    wrapper = mount(ProfileTabPanel, {
      global: {
        stubs: {
          'q-btn': {
            name: 'QBtn',
            template: '<button><slot></slot></button>',
            props: ['flat', 'class', 'padding', 'no-caps']
          }
        }
      }
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('user data display', () => {
    it('should render user profile information', () => {
      expect(wrapper.text()).toContain('John Doe')
      expect(wrapper.text()).toContain('<EMAIL>')
      expect(wrapper.text()).toContain('John')
      expect(wrapper.text()).toContain('Doe')
      expect(wrapper.text()).toContain('1')
    })

    it('should display profile picture when available', () => {
      const img = wrapper.find('img')
      expect(img.exists()).toBe(true)
      expect(img.attributes('src')).toBe('https://example.com/avatar.jpg')
      expect(img.attributes('alt')).toContain('John Doe profile picture')
    })

    it('should show user icon when no profile picture', async () => {
      mockUserProfile.currentUser.value.profilePicture = null
      await wrapper.vm.$nextTick()
      
      // Should show UserIcon instead of img
      const userIcon = wrapper.find('[data-testid="user-icon"]')
      expect(userIcon.exists()).toBe(true)
    })

    it('should format dates correctly', () => {
      expect(wrapper.text()).toContain('January 15, 2022')
      expect(wrapper.text()).toContain('January 15, 2023')
    })
  })

  describe('loading states', () => {
    it('should show loading state when user is loading', async () => {
      mockUserProfile.userLoading.value = true
      mockUserProfile.currentUser.value = null
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Loading profile...')
    })

    it('should show error state when there is an error', async () => {
      mockUserProfile.userError.value = { message: 'Failed to load profile' }
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Error loading profile')
      expect(wrapper.text()).toContain('Failed to load profile')
    })

    it('should show no data message when user is null', async () => {
      mockUserProfile.currentUser.value = null
      mockUserProfile.userLoading.value = false
      mockUserProfile.userError.value = null
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('No profile data available')
    })
  })

  describe('user interactions', () => {
    it('should emit edit-profile event when edit button is clicked', async () => {
      const editButton = wrapper.find('button')
      await editButton.trigger('click')
      
      expect(wrapper.emitted('edit-profile')).toBeTruthy()
      expect(wrapper.emitted('edit-profile')).toHaveLength(1)
    })

    it('should call refetch when try again button is clicked', async () => {
      mockUserProfile.userError.value = { message: 'Network error' }
      await wrapper.vm.$nextTick()
      
      const tryAgainButton = wrapper.find('button')
      await tryAgainButton.trigger('click')
      
      expect(mockUserProfile.refetchUser).toHaveBeenCalled()
    })
  })

  describe('fallback values', () => {
    it('should handle missing name fields gracefully', async () => {
      mockUserProfile.currentUser.value = {
        ...mockUserProfile.currentUser.value,
        firstName: null,
        lastName: null,
        fullName: null
      }
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('No name set')
      expect(wrapper.text()).toContain('Not provided')
    })

    it('should handle missing optional fields', async () => {
      mockUserProfile.currentUser.value = {
        ...mockUserProfile.currentUser.value,
        profilePicture: null,
        createdAt: null,
        updatedAt: null
      }
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Not available')
    })
  })

  describe('accessibility', () => {
    it('should have proper alt text for profile image', () => {
      const img = wrapper.find('img')
      expect(img.attributes('alt')).toBe('John Doe profile picture')
    })

    it('should have proper heading structure', () => {
      expect(wrapper.find('h3').text()).toBe('John Doe')
    })

    it('should have proper labels for data fields', () => {
      const dtElements = wrapper.findAll('dt')
      const labels = dtElements.map(dt => dt.text())
      
      expect(labels).toContain('Full name')
      expect(labels).toContain('First name')
      expect(labels).toContain('Last name')
      expect(labels).toContain('Email address')
      expect(labels).toContain('Account created')
      expect(labels).toContain('Last updated')
      expect(labels).toContain('Organisation ID')
    })
  })
})
