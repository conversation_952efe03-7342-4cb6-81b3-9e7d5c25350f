import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { mount } from '@vue/test-utils'
import { ref } from 'vue'
import PasswordChangeForm from 'src/components/PasswordChangeForm.vue'

// Mock the user profile composable
const mockUpdatePassword = jest.fn()
const mockClearMessages = jest.fn()

const mockUserProfile = {
  updatePassword: mockUpdatePassword,
  passwordLoading: ref(false),
  passwordSuccess: ref(false),
  passwordError: ref(''),
  clearMessages: mockClearMessages
}

jest.mock('src/composables/use-user-profile', () => ({
  useUserProfile: () => mockUserProfile,
  validatePasswordStrength: (password) => {
    if (!password) return { score: 0, strength: '', checks: {} }
    
    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    }
    
    const score = Object.values(checks).filter(Boolean).length
    let strength = ''
    if (score <= 1) strength = 'Very Weak'
    else if (score === 2) strength = 'Weak'
    else if (score === 3) strength = 'Fair'
    else if (score === 4) strength = 'Good'
    else strength = 'Strong'
    
    return { score, strength, checks }
  }
}))

// Mock PageHeading component
jest.mock('src/components/PageHeading.vue', () => ({
  name: 'PageHeading',
  template: '<div class="page-heading"><slot name="heading"></slot><slot name="description"></slot></div>'
}))

// Mock Vuelidate
jest.mock('@vuelidate/core', () => ({
  default: jest.fn(() => ({
    $validate: jest.fn(() => Promise.resolve(true)),
    $reset: jest.fn(),
    currentPassword: { $error: false, $errors: [] },
    newPassword: { $error: false, $errors: [] },
    confirmPassword: { $error: false, $errors: [] }
  }))
}))

describe('PasswordChangeForm', () => {
  let wrapper

  beforeEach(() => {
    jest.clearAllMocks()
    
    wrapper = mount(PasswordChangeForm, {
      global: {
        stubs: {
          'q-btn': {
            name: 'QBtn',
            template: '<button type="button" :disabled="disabled"><slot></slot></button>',
            props: ['flat', 'class', 'padding', 'no-caps', 'disabled', 'loading']
          },
          'q-input': {
            name: 'QInput',
            template: '<input :value="modelValue" :type="type" @input="$emit(\'update:modelValue\', $event.target.value)" />',
            props: ['modelValue', 'label', 'type', 'outlined', 'dense', 'error', 'error-message'],
            emits: ['update:modelValue']
          }
        }
      }
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('form rendering', () => {
    it('should render password input fields', () => {
      const inputs = wrapper.findAll('input[type="password"]')
      expect(inputs).toHaveLength(3) // current, new, confirm
    })

    it('should render password strength indicator', () => {
      expect(wrapper.find('[data-testid="password-strength"]').exists()).toBe(true)
    })

    it('should render security requirements checklist', () => {
      expect(wrapper.text()).toContain('At least 8 characters')
      expect(wrapper.text()).toContain('One uppercase letter')
      expect(wrapper.text()).toContain('One lowercase letter')
      expect(wrapper.text()).toContain('One number')
      expect(wrapper.text()).toContain('One special character')
    })
  })

  describe('password strength validation', () => {
    it('should show very weak for simple password', async () => {
      const newPasswordInput = wrapper.findAll('input')[1]
      await newPasswordInput.setValue('abc')
      
      expect(wrapper.text()).toContain('Very Weak')
    })

    it('should show weak for password with only lowercase and length', async () => {
      const newPasswordInput = wrapper.findAll('input')[1]
      await newPasswordInput.setValue('password')
      
      expect(wrapper.text()).toContain('Weak')
    })

    it('should show strong for complex password', async () => {
      const newPasswordInput = wrapper.findAll('input')[1]
      await newPasswordInput.setValue('Password123!')
      
      expect(wrapper.text()).toContain('Strong')
    })

    it('should update strength indicator bars', async () => {
      const newPasswordInput = wrapper.findAll('input')[1]
      await newPasswordInput.setValue('Password123!')
      
      const strengthBars = wrapper.findAll('[data-testid="strength-bar"]')
      expect(strengthBars).toHaveLength(5)
    })

    it('should update security requirements checklist', async () => {
      const newPasswordInput = wrapper.findAll('input')[1]
      await newPasswordInput.setValue('Password123!')
      
      // All requirements should be met
      const checkIcons = wrapper.findAll('[data-testid="check-icon"]')
      expect(checkIcons).toHaveLength(5)
    })
  })

  describe('form validation', () => {
    it('should validate required fields', async () => {
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      // Should not submit with empty fields
      expect(mockUpdatePassword).not.toHaveBeenCalled()
    })

    it('should validate password confirmation match', async () => {
      const inputs = wrapper.findAll('input')
      await inputs[0].setValue('currentpass')
      await inputs[1].setValue('newpass123')
      await inputs[2].setValue('differentpass')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockUpdatePassword).not.toHaveBeenCalled()
    })

    it('should validate minimum password length', async () => {
      const inputs = wrapper.findAll('input')
      await inputs[0].setValue('currentpass')
      await inputs[1].setValue('short')
      await inputs[2].setValue('short')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockUpdatePassword).not.toHaveBeenCalled()
    })
  })

  describe('form submission', () => {
    it('should submit form with valid data', async () => {
      mockUpdatePassword.mockResolvedValue('Password changed successfully')
      
      const inputs = wrapper.findAll('input')
      await inputs[0].setValue('currentpass')
      await inputs[1].setValue('newpass123')
      await inputs[2].setValue('newpass123')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockClearMessages).toHaveBeenCalled()
      expect(mockUpdatePassword).toHaveBeenCalledWith({
        currentPassword: 'currentpass',
        newPassword: 'newpass123',
        confirmPassword: 'newpass123'
      })
    })

    it('should show loading state during submission', async () => {
      mockUserProfile.passwordLoading.value = true
      await wrapper.vm.$nextTick()
      
      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('disabled')).toBeDefined()
    })

    it('should show success message on successful change', async () => {
      mockUpdatePassword.mockResolvedValue('Password changed successfully')
      
      const inputs = wrapper.findAll('input')
      await inputs[0].setValue('currentpass')
      await inputs[1].setValue('newpass123')
      await inputs[2].setValue('newpass123')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Password changed successfully')
    })

    it('should handle submission errors', async () => {
      const errorMessage = 'Current password is incorrect'
      mockUpdatePassword.mockRejectedValue(new Error(errorMessage))
      mockUserProfile.passwordError.value = errorMessage
      
      const inputs = wrapper.findAll('input')
      await inputs[0].setValue('wrongpass')
      await inputs[1].setValue('newpass123')
      await inputs[2].setValue('newpass123')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain(errorMessage)
    })

    it('should clear form after successful submission', async () => {
      mockUpdatePassword.mockResolvedValue('Password changed successfully')
      
      const inputs = wrapper.findAll('input')
      await inputs[0].setValue('currentpass')
      await inputs[1].setValue('newpass123')
      await inputs[2].setValue('newpass123')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      // Form should be cleared
      expect(inputs[0].element.value).toBe('')
      expect(inputs[1].element.value).toBe('')
      expect(inputs[2].element.value).toBe('')
    })
  })

  describe('accessibility', () => {
    it('should have proper form labels', () => {
      expect(wrapper.text()).toContain('Current Password')
      expect(wrapper.text()).toContain('New Password')
      expect(wrapper.text()).toContain('Confirm New Password')
    })

    it('should show error messages with proper ARIA attributes', async () => {
      wrapper.vm.errorMessage = 'Test error'
      await wrapper.vm.$nextTick()
      
      const errorElement = wrapper.find('[role="alert"]')
      expect(errorElement.exists()).toBe(true)
      expect(errorElement.text()).toContain('Test error')
    })

    it('should have proper ARIA labels for strength indicator', () => {
      const strengthIndicator = wrapper.find('[data-testid="password-strength"]')
      expect(strengthIndicator.attributes('aria-label')).toBeTruthy()
    })
  })
})
