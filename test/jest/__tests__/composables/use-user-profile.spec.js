import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { ref } from 'vue'
import { useUserProfile, validatePasswordStrength, formatUserDate } from 'src/composables/use-user-profile'

// Mock Vue Apollo composables
const mockQuery = jest.fn()
const mockMutation = jest.fn()
const mockRefetch = jest.fn()

jest.mock('@vue/apollo-composable', () => ({
  useQuery: jest.fn(() => ({
    result: ref(null),
    loading: ref(false),
    error: ref(null),
    refetch: mockRefetch
  })),
  useMutation: jest.fn(() => ({
    mutate: mockMutation,
    loading: ref(false),
    onDone: jest.fn(),
    onError: jest.fn()
  }))
}))

// Mock cache-first query composable
jest.mock('src/composables/use-cache-first-query', () => ({
  useCacheFirstQuery: jest.fn(() => ({
    result: ref({
      currentUser: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        fullName: '<PERSON>e',
        profilePicture: 'https://example.com/avatar.jpg',
        createdAt: '2022-01-15T10:30:00Z',
        updatedAt: '2023-01-15T10:30:00Z',
        organisationId: 1
      }
    }),
    loading: ref(false),
    error: ref(null),
    refetch: mockRefetch
  }))
}))

describe('useUserProfile', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('user data', () => {
    it('should provide current user data', () => {
      const { currentUser } = useUserProfile()
      
      expect(currentUser.value).toEqual({
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        fullName: 'John Doe',
        profilePicture: 'https://example.com/avatar.jpg',
        createdAt: '2022-01-15T10:30:00Z',
        updatedAt: '2023-01-15T10:30:00Z',
        organisationId: 1
      })
    })

    it('should compute display name correctly', () => {
      const { displayName } = useUserProfile()
      
      expect(displayName.value).toBe('John Doe')
    })

    it('should compute user initials correctly', () => {
      const { userInitials } = useUserProfile()
      
      expect(userInitials.value).toBe('JD')
    })

    it('should determine authentication status', () => {
      const { isAuthenticated } = useUserProfile()
      
      expect(isAuthenticated.value).toBe(true)
    })
  })

  describe('profile updates', () => {
    it('should update profile successfully', async () => {
      mockMutation.mockResolvedValue({
        data: {
          updateUserProfile: {
            success: true,
            user: { id: '1', firstName: 'Jane' },
            errors: []
          }
        }
      })

      const { updateProfile } = useUserProfile()
      
      const result = await updateProfile({
        firstName: 'Jane',
        lastName: 'Doe',
        email: '<EMAIL>'
      })

      expect(mockMutation).toHaveBeenCalledWith({
        input: {
          firstName: 'Jane',
          lastName: 'Doe',
          email: '<EMAIL>'
        }
      })
      expect(result).toEqual({ id: '1', firstName: 'Jane' })
    })

    it('should handle profile update errors', async () => {
      mockMutation.mockResolvedValue({
        data: {
          updateUserProfile: {
            success: false,
            user: null,
            errors: [{ message: 'Email already exists', path: ['email'] }]
          }
        }
      })

      const { updateProfile } = useUserProfile()
      
      await expect(updateProfile({
        email: '<EMAIL>'
      })).rejects.toThrow('Email already exists')
    })
  })

  describe('password changes', () => {
    it('should change password successfully', async () => {
      mockMutation.mockResolvedValue({
        data: {
          changePassword: {
            success: true,
            message: 'Password changed successfully',
            errors: []
          }
        }
      })

      const { updatePassword } = useUserProfile()
      
      const result = await updatePassword({
        currentPassword: 'oldpass',
        newPassword: 'newpass123',
        confirmPassword: 'newpass123'
      })

      expect(mockMutation).toHaveBeenCalledWith({
        input: {
          currentPassword: 'oldpass',
          newPassword: 'newpass123',
          confirmPassword: 'newpass123'
        }
      })
      expect(result).toBe('Password changed successfully')
    })

    it('should handle password change errors', async () => {
      mockMutation.mockResolvedValue({
        data: {
          changePassword: {
            success: false,
            message: null,
            errors: [{ message: 'Current password is incorrect', path: ['currentPassword'] }]
          }
        }
      })

      const { updatePassword } = useUserProfile()
      
      await expect(updatePassword({
        currentPassword: 'wrongpass',
        newPassword: 'newpass123',
        confirmPassword: 'newpass123'
      })).rejects.toThrow('Current password is incorrect')
    })
  })
})

describe('validatePasswordStrength', () => {
  it('should return zero score for empty password', () => {
    const result = validatePasswordStrength('')
    
    expect(result.score).toBe(0)
    expect(result.strength).toBe('')
    expect(result.checks.length).toBe(false)
  })

  it('should validate weak password', () => {
    const result = validatePasswordStrength('password')
    
    expect(result.score).toBe(2) // length + lowercase
    expect(result.strength).toBe('Weak')
    expect(result.checks.length).toBe(true)
    expect(result.checks.lowercase).toBe(true)
    expect(result.checks.uppercase).toBe(false)
  })

  it('should validate strong password', () => {
    const result = validatePasswordStrength('Password123!')
    
    expect(result.score).toBe(5) // all criteria met
    expect(result.strength).toBe('Strong')
    expect(result.checks.length).toBe(true)
    expect(result.checks.uppercase).toBe(true)
    expect(result.checks.lowercase).toBe(true)
    expect(result.checks.number).toBe(true)
    expect(result.checks.special).toBe(true)
  })

  it('should validate good password', () => {
    const result = validatePasswordStrength('Password123')
    
    expect(result.score).toBe(4) // missing special character
    expect(result.strength).toBe('Good')
    expect(result.checks.special).toBe(false)
  })
})

describe('formatUserDate', () => {
  it('should format valid date string', () => {
    const result = formatUserDate('2022-01-15T10:30:00Z')
    
    expect(result).toMatch(/January 15, 2022/)
  })

  it('should handle invalid date', () => {
    const result = formatUserDate('invalid-date')
    
    expect(result).toBe('Invalid date')
  })

  it('should handle null/undefined date', () => {
    expect(formatUserDate(null)).toBe('Not available')
    expect(formatUserDate(undefined)).toBe('Not available')
  })
})
